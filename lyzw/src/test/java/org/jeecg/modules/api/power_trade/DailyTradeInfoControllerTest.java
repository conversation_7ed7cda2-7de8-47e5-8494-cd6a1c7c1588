package org.jeecg.modules.api.power_trade;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.RequestOptions;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 日交易信息接口自动化测试
 * 使用Playwright进行API接口测试
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles("test")
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class DailyTradeInfoControllerTest {

    private static Playwright playwright;
    private static APIRequestContext request;
    private static final String BASE_URL = "http://localhost:8080";
    private static final String API_PATH = "/api/power_trade/daily_trade/list";

    @BeforeAll
    static void setUp() {
        // 初始化Playwright
        playwright = Playwright.create();
        request = playwright.request().newContext(new APIRequest.NewContextOptions()
                .setBaseURL(BASE_URL));
        
        log.info("Playwright初始化完成，开始API测试");
    }

    @AfterAll
    static void tearDown() {
        if (request != null) {
            request.dispose();
        }
        if (playwright != null) {
            playwright.close();
        }
        log.info("Playwright测试完成，资源已清理");
    }

    @Test
    @Order(1)
    @DisplayName("测试默认参数查询 - 应该返回当月数据")
    void testDefaultQuery() {
        try {
            APIResponse response = request.get(API_PATH);
            
            // 验证响应状态
            assertEquals(200, response.status(), "API响应状态应为200");
            
            // 验证响应内容
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("默认查询测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("默认查询测试失败", e);
            fail("默认查询测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试省份参数查询 - 安徽省数据")
    void testProvinceQuery() {
        try {
            APIResponse response = request.get(API_PATH + "?provinceId=2");
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("安徽省查询测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("安徽省查询测试失败", e);
            fail("安徽省查询测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试日期范围查询")
    void testDateRangeQuery() {
        try {
            String queryParams = "?provinceId=2&startDate=2024-07-01&endDate=2024-07-07";
            APIResponse response = request.get(API_PATH + queryParams);
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("日期范围查询测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("日期范围查询测试失败", e);
            fail("日期范围查询测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试电站类型过滤 - 光伏")
    void testStationTypeFilter() {
        try {
            String queryParams = "?provinceId=2&stationType=photovoltaic";
            APIResponse response = request.get(API_PATH + queryParams);
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("光伏电站类型查询测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("光伏电站类型查询测试失败", e);
            fail("光伏电站类型查询测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试储能数据查询")
    void testStorageQuery() {
        try {
            String queryParams = "?provinceId=2&stationType=storage";
            APIResponse response = request.get(API_PATH + queryParams);
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("储能数据查询测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("储能数据查询测试失败", e);
            fail("储能数据查询测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试江苏省储能限制 - 应该返回空数据")
    void testJiangsuStorageRestriction() {
        try {
            String queryParams = "?provinceId=1&stationType=storage";
            APIResponse response = request.get(API_PATH + queryParams);
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            // 江苏省不支持储能，应该返回空数组
            assertTrue(responseText.contains("\"result\":[]"), "江苏省储能查询应返回空数组");
            
            log.info("江苏省储能限制测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("江苏省储能限制测试失败", e);
            fail("江苏省储能限制测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(7)
    @DisplayName("测试全国数据聚合")
    void testNationalAggregation() {
        try {
            String queryParams = "?provinceId=0";
            APIResponse response = request.get(API_PATH + queryParams);
            
            assertEquals(200, response.status(), "API响应状态应为200");
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            assertTrue(responseText.contains("\"success\":true"), "响应应包含success:true");
            
            log.info("全国数据聚合测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("全国数据聚合测试失败", e);
            fail("全国数据聚合测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(8)
    @DisplayName("测试参数验证 - 无效省份ID")
    void testInvalidProvinceId() {
        try {
            String queryParams = "?provinceId=999";
            APIResponse response = request.get(API_PATH + queryParams);
            
            // 可能返回200但包含错误信息，或者返回400
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            
            // 检查是否包含错误信息
            boolean hasError = responseText.contains("\"success\":false") || 
                              responseText.contains("不支持的省份") ||
                              response.status() == 400;
            assertTrue(hasError, "无效省份ID应该返回错误");
            
            log.info("无效省份ID测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("无效省份ID测试失败", e);
            fail("无效省份ID测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(9)
    @DisplayName("测试参数验证 - 无效日期格式")
    void testInvalidDateFormat() {
        try {
            String queryParams = "?provinceId=2&startDate=2024/07/01";
            APIResponse response = request.get(API_PATH + queryParams);
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            
            // 检查是否包含日期格式错误信息
            boolean hasError = responseText.contains("\"success\":false") || 
                              responseText.contains("日期格式错误") ||
                              response.status() == 400;
            assertTrue(hasError, "无效日期格式应该返回错误");
            
            log.info("无效日期格式测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("无效日期格式测试失败", e);
            fail("无效日期格式测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(10)
    @DisplayName("测试参数验证 - 无效电站类型")
    void testInvalidStationType() {
        try {
            String queryParams = "?provinceId=2&stationType=invalid";
            APIResponse response = request.get(API_PATH + queryParams);
            
            String responseText = response.text();
            assertNotNull(responseText, "响应内容不应为空");
            
            // 检查是否包含电站类型错误信息
            boolean hasError = responseText.contains("\"success\":false") || 
                              responseText.contains("电站类型参数错误") ||
                              response.status() == 400;
            assertTrue(hasError, "无效电站类型应该返回错误");
            
            log.info("无效电站类型测试通过 - 响应: {}", responseText);
            
        } catch (Exception e) {
            log.error("无效电站类型测试失败", e);
            fail("无效电站类型测试失败: " + e.getMessage());
        }
    }
}
