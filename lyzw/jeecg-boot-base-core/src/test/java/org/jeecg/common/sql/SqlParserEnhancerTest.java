package org.jeecg.common.sql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SQL解析增强器测试类
 * 
 * <AUTHOR>
 * @since 2025-07-26
 */
@ExtendWith(MockitoExtension.class)
class SqlParserEnhancerTest {
    
    private SqlParserEnhancer sqlParserEnhancer;
    
    @BeforeEach
    void setUp() {
        sqlParserEnhancer = new SqlParserEnhancer();
    }
    
    @Test
    void testSimpleSqlParsing() {
        // 测试简单SQL解析
        String simpleSql = "SELECT * FROM users WHERE id = 1";
        SqlParseResult result = sqlParserEnhancer.parseStatement(simpleSql);
        
        assertTrue(result.isSuccess());
        assertNotNull(result.getParsedStatement());
        assertFalse(result.isFallbackUsed());
        assertTrue(result.getComplexityScore() < 50);
    }
    
    @Test
    void testComplexJoinSqlParsing() {
        // 测试复杂JOIN SQL解析
        String complexSql = "SELECT e.*, p.value FROM day_ahead_node_clear_electricity e " +
                           "LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id " +
                           "AND e.date = p.date AND e.time = p.time " +
                           "WHERE e.station_id = 1 GROUP BY e.date ORDER BY e.date";
        
        SqlParseResult result = sqlParserEnhancer.parseStatement(complexSql);
        
        assertTrue(result.isSuccess());
        assertTrue(result.getComplexityScore() > 30);
    }
    
    @Test
    void testProblematicSqlFallback() {
        // 测试导致解析失败的SQL，验证降级策略
        String problematicSql = "SELECT DATE_FORMAT(e.date, '%Y-%m') as month, " +
                               "SUM(IFNULL(e.value, 0)) as totalValue " +
                               "FROM day_ahead_node_clear_electricity e " +
                               "LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id " +
                               "WHERE DATE_FORMAT(e.date, '%Y') = '2024' " +
                               "GROUP BY DATE_FORMAT(e.date, '%Y-%m') ORDER BY month";
        
        SqlParseResult result = sqlParserEnhancer.parseStatement(problematicSql);
        
        assertTrue(result.isSuccess()); // 即使原SQL解析失败，降级策略应该成功
        assertNotNull(result.getProcessedSql());
    }
    
    @Test
    void testNullAndEmptySql() {
        // 测试空SQL处理
        SqlParseResult nullResult = sqlParserEnhancer.parseStatement(null);
        assertFalse(nullResult.isSuccess());
        assertEquals("SQL不能为空", nullResult.getErrorMessage());
        
        SqlParseResult emptyResult = sqlParserEnhancer.parseStatement("");
        assertFalse(emptyResult.isSuccess());
        assertEquals("SQL不能为空", emptyResult.getErrorMessage());
    }
    
    @Test
    void testComplexityScoring() {
        // 测试复杂度评分
        String simpleSelect = "SELECT * FROM users";
        assertEquals(0, sqlParserEnhancer.getComplexityScore(simpleSelect));
        
        String joinSql = "SELECT * FROM users u JOIN orders o ON u.id = o.user_id";
        assertTrue(sqlParserEnhancer.getComplexityScore(joinSql) > 10);
        
        String complexSql = "SELECT u.*, COUNT(o.id) FROM users u " +
                           "LEFT JOIN orders o ON u.id = o.user_id " +
                           "GROUP BY u.id HAVING COUNT(o.id) > 5 " +
                           "ORDER BY COUNT(o.id) DESC";
        assertTrue(sqlParserEnhancer.getComplexityScore(complexSql) > 50);
    }
    
    @Test
    void testSpecialHandlingDetection() {
        // 测试特殊处理检测
        String simpleSelect = "SELECT * FROM users";
        assertFalse(sqlParserEnhancer.needsSpecialHandling(simpleSelect));
        
        String joinSql = "SELECT * FROM users u LEFT JOIN orders o ON u.id = o.user_id";
        assertTrue(sqlParserEnhancer.needsSpecialHandling(joinSql));
        
        String groupBySql = "SELECT COUNT(*) FROM users GROUP BY status HAVING COUNT(*) > 10";
        assertTrue(sqlParserEnhancer.needsSpecialHandling(groupBySql));
        
        String subquerySql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders)";
        assertTrue(sqlParserEnhancer.needsSpecialHandling(subquerySql));
    }
    
    @Test
    void testSqlPreprocessing() {
        // 测试SQL预处理
        String sqlWithComments = "SELECT * /* comment */ FROM users -- line comment\nWHERE id = 1";
        String processed = sqlParserEnhancer.preprocessSql(sqlWithComments);
        
        assertFalse(processed.contains("/*"));
        assertFalse(processed.contains("--"));
        assertTrue(processed.contains("SELECT * FROM users"));
    }
    
    @Test
    void testCacheFunction() {
        // 测试缓存功能
        String sql = "SELECT * FROM users WHERE id = 1";
        
        // 第一次解析
        SqlParseResult result1 = sqlParserEnhancer.parseStatement(sql);
        assertTrue(result1.isSuccess());
        
        // 第二次解析应该命中缓存
        SqlParseResult result2 = sqlParserEnhancer.parseStatement(sql);
        assertTrue(result2.isSuccess());
        
        // 检查缓存统计
        String stats = sqlParserEnhancer.getCacheStats();
        assertTrue(stats.contains("命中: 1"));
        
        // 清理缓存
        sqlParserEnhancer.clearCache();
        String statsAfterClear = sqlParserEnhancer.getCacheStats();
        assertTrue(statsAfterClear.contains("命中: 0"));
    }
    
    @Test
    void testElectricityDataSqlParsing() {
        // 测试实际的电力数据查询SQL
        String electricitySql = "SELECT DATE_FORMAT(e.date, '%Y-%m') as month, " +
                               "SUM(IFNULL(e.value, 0)) as totalValue, " +
                               "AVG(IFNULL(p.value, 0)) as avgPrice, " +
                               "SUM(IFNULL(e.value, 0) * IFNULL(p.value, 0)) as totalFee " +
                               "FROM day_ahead_node_clear_electricity e " +
                               "LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id " +
                               "AND e.date = p.date AND e.time = p.time " +
                               "WHERE DATE_FORMAT(e.date, '%Y') = '2024' " +
                               "GROUP BY DATE_FORMAT(e.date, '%Y-%m') ORDER BY month";
        
        SqlParseResult result = sqlParserEnhancer.parseStatement(electricitySql);
        
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedSql());
        assertTrue(result.getComplexityScore() > 0);
        
        // 验证这是一个需要特殊处理的复杂SQL
        assertTrue(sqlParserEnhancer.needsSpecialHandling(electricitySql));
    }
}