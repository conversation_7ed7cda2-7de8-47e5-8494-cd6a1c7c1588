package org.jeecg.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtils {
    /**
     * 安全除法（默认保留4位小数，四舍五入）
     */
    public static BigDecimal safeDivide(BigDecimal dividend, BigDecimal divisor) {
        return safeDivide(dividend, divisor, 2, RoundingMode.HALF_UP);
    }

    /**
     * 安全除法（自定义精度和舍入模式）
     */
    public static BigDecimal safeDivide(BigDecimal dividend, BigDecimal divisor,
                                        int scale, RoundingMode roundingMode) {
        if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO; // 除数为零或null时返回0
        }
        return dividend.divide(divisor, scale, roundingMode);
    }
}
