package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: Entity基类
 * @Author: <EMAIL>
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuestionEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "问题")
    private String question;

    @ApiModelProperty(value = "选项")
    private List<Map<String,String>> options;

    @ApiModelProperty(value = "正确答案")
    private String trueAnswer;

    @ApiModelProperty(value = "句子的中文含义")
    private String questionChMeaning;

}
