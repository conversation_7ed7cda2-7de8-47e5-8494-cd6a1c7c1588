package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.DayAheadClearPower;
import org.jeecg.modules.api.power_trade.mapper.DayAheadClearPowerMapper;
import org.jeecg.modules.api.power_trade.service.DayAheadClearPowerService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DayAheadClearPowerServiceImpl extends ServiceImpl<DayAheadClearPowerMapper, DayAheadClearPower> implements DayAheadClearPowerService {

    public List<DayAheadClearPower> getDayDetails(Long stationId, String date) {
        return baseMapper.selectByDay(stationId, date);
    }

    public List<Map<String, Object>> getMonthlySum(Long stationId, String yearMonth) {
        return baseMapper.selectMonthlySum(stationId, yearMonth);
    }

    public List<Map<String, Object>> getYearlySum(Long stationId, String year) {
        return baseMapper.selectYearlySum(stationId, year);
    }
}