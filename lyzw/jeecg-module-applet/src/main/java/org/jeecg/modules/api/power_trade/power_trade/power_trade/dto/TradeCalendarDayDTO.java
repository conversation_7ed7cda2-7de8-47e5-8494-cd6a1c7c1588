package org.jeecg.modules.api.power_trade.dto;

import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.entity.TradeDiary;

import java.util.ArrayList;
import java.util.List;

/**
 * 每日交易日历数据
 */
@Data
public class TradeCalendarDayDTO {

    /**
     * 日期 yyyy-MM-dd
     */
    private String date;
    
    /**
     * 省份标签列表
     */
    private List<ProvinceTagDTO> provinceTags = new ArrayList<>();

    
    /**
     * 交易日历记录列表
     */
    private List<TradeCalendarRecord> calendarRecords = new ArrayList<>();

    @Data
    public static class ProvinceTagDTO {
        /**
         * 省份ID
         */
        private Integer provinceId;

        /**
         * 省份名称
         */
        private String provinceName;
    }
} 