package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 交易日历查询参数
 */
@Data
@ApiModel(value = "交易日历查询参数")
public class TradeCalendarQueryDTO {

    /**
     * 开始日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "开始日期，格式：yyyy-MM-dd")
    private String startDate;
    
    /**
     * 结束日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "结束日期，格式：yyyy-MM-dd")
    private String endDate;


    @ApiModelProperty(value = "省份ID (0-全国, 1-江苏, 2-安徽)")
    private Integer provinceId;
} 