package org.jeecg.modules.api.power_trade.enums;

/**
 * 结算单文件类型枚举
 */
public enum SettlementFileTypeEnum {

    /**
     * 省内日清分明细结算单
     */
    SETTLE_DAY_DETAILS(1, "省内日清分明细结算单"),

    /**
     * 日省内日清分结算单
     */
    SETTLE_DAY_SUM(2, "日省内日清分结算单"),

    /**
     * 月统推发电侧结算单
     */
    SETTLE_MONTH_DETAILS(3, "月统推发电侧结算单"),

    /**
     * 月统推独立储能结算单
     */
    SETTLE_MONTH_STORAGE_DETAILS(4, "月统推独立储能结算单");

    private final Integer code;
    private final String description;

    SettlementFileTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 枚举值
     */
    public static SettlementFileTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SettlementFileTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为月统推结算单
     * @return true-月统推结算单, false-日结算单
     */
    public boolean isMonthlySettlement() {
        return this == SETTLE_MONTH_DETAILS || this == SETTLE_MONTH_STORAGE_DETAILS;
    }

    /**
     * 判断是否为日结算单
     * @return true-日结算单, false-月统推结算单
     */
    public boolean isDailySettlement() {
        return this == SETTLE_DAY_DETAILS || this == SETTLE_DAY_SUM;
    }

    /**
     * 判断是否为发电侧结算单
     * @return true-发电侧结算单, false-储能结算单
     */
    public boolean isPowerGenerationSettlement() {
        return this == SETTLE_DAY_DETAILS || this == SETTLE_DAY_SUM || this == SETTLE_MONTH_DETAILS;
    }

    /**
     * 判断是否为储能结算单
     * @return true-储能结算单, false-发电侧结算单
     */
    public boolean isEnergyStorageSettlement() {
        return this == SETTLE_MONTH_STORAGE_DETAILS;
    }
}
