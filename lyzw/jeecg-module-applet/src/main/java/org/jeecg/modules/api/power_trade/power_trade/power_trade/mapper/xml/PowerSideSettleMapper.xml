<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper">

    <!-- 根据电站ID和时间范围获取累计结算电量和交易均价 -->
    <select id="getStationSettlementSummary" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementElectricity,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementElectricFee,
            CASE
                WHEN SUM(pss.settlement_electricity) > 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as avgTradePrice,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
          AND fsr.settle_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <!-- 根据省份ID和时间范围获取所有电站的结算汇总数据 -->
    <select id="getProvinceStationSettlementSummary" resultType="java.util.Map">
        SELECT
            s.id as stationId,
            s.name as stationName,
            s.type as stationType,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementElectricity,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementElectricFee,
            CASE
                WHEN SUM(pss.settlement_electricity) > 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as avgTradePrice
        FROM station s
        LEFT JOIN file_station_relation fsr ON s.id = fsr.station_id
        LEFT JOIN power_side_settle pss ON fsr.id = pss.file_id
        WHERE s.province_id = #{provinceId}
          AND (fsr.type = #{fileType} OR fsr.type IS NULL)
          AND (
            (fsr.type IN (3, 4) AND DATE_FORMAT(fsr.settle_date, '%Y-%m') BETWEEN #{startYearMonth} AND #{endYearMonth})
            OR
            (fsr.type IN (1, 2) AND DATE(fsr.settle_date) BETWEEN #{startDate} AND #{endDate})
            OR
            fsr.settle_date IS NULL
          )
        GROUP BY s.id, s.name, s.type
        ORDER BY s.id
    </select>

    <!-- 获取电站年度交易数据汇总 -->
    <select id="getStationYearlyTradeData" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND fsr.type IN (3, 4)
    </select>

    <!-- 获取电站月度交易数据汇总 -->
    <select id="getStationMonthlyTradeData" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND MONTH(fsr.settle_date) = #{month}
        AND fsr.type IN (3, 4)
    </select>

    <!-- 根据电站类型获取年度交易数据 -->
    <select id="getStationYearlyTradeDataByType" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND s.type = #{stationType}
        AND fsr.type = #{settlementType}
    </select>

    <!-- 获取电站年度交易电量信息（用于电站详情） -->
    <select id="getStationYearlyTradingInfo" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
            -- 月度交易电量：settlement_electricity字段求和
            COALESCE(SUM(pss.settlement_electricity), 0) as monthlyElectricity,
            -- 月度交易均价：settlement_electric_fee求和 / settlement_electricity求和
            CASE
                WHEN SUM(pss.settlement_electricity) &gt; 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as monthlyAveragePrice,
            -- 月度交易总电费：settlement_electric_fee字段求和
            COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlyTotalFee,
            -- 其他辅助字段
            COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
            COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
            COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
            COUNT(*) as recordCount
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
            AND YEAR(fsr.settle_date) = #{year}
            AND pss.settlement_electricity IS NOT NULL
            AND pss.settlement_electricity &gt; 0
        GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
        ORDER BY month ASC
    </select>

    <!-- 获取电站月度交易数据 -->
<select id="getStationMonthlyTradingInfo" resultType="java.util.Map">
    SELECT
    -- 月度交易总电量
    COALESCE(SUM(pss.settlement_electricity), 0) as monthlyTotalElectricity,

    -- 月度交易均价
    CASE
    WHEN SUM(pss.settlement_electricity) &gt; 0
    THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
    ELSE 0
    END as monthlyAveragePrice,

    -- 月度交易总电费
    COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlyTotalFee,

    -- 实际上网电量
    COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,

    -- 合同电量
    COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,

    -- 偏差电量
    COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,

    -- 统计信息
    COUNT(*) as recordCount

    FROM power_side_settle pss
    INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
    WHERE fsr.station_id = #{stationId}
    AND DATE_FORMAT(fsr.settle_date, '%Y-%m') = #{yearMonth}  <!-- 关键：日期格式匹配 -->
    AND pss.settlement_electricity IS NOT NULL
    AND pss.settlement_electricity &gt; 0
</select>

<!-- 调试查询：验证数据关联 -->
<select id="debugStationDataRelation" resultType="java.util.Map">
    SELECT
        fsr.id as fileId,
        fsr.station_id as stationId,
        fsr.settle_date as settleDate,
        fsr.type as fileType,
        COUNT(pss.id) as settlementRecordCount,
        SUM(pss.settlement_electricity) as totalElectricity,
        SUM(pss.settlement_electric_fee) as totalFee
    FROM file_station_relation fsr
    LEFT JOIN power_side_settle pss ON fsr.id = pss.file_id
    WHERE fsr.station_id = #{stationId}
    AND YEAR(fsr.settle_date) = #{year}
    GROUP BY fsr.id, fsr.station_id, fsr.settle_date, fsr.type
    ORDER BY fsr.settle_date ASC
</select>

<!-- 获取电站月度交易信息（光伏/风电电站） -->
<select id="getStationMonthlyTradingInfoForPvWind" resultType="java.util.Map">
    SELECT
        DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
        DATE_FORMAT(fsr.settle_date, '%m') as monthNumber,
        -- 实际上网电量
        COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
        -- 结算电量
        COALESCE(SUM(pss.settlement_electricity), 0) as settlementElectricity,
        -- 合同电量
        COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
        -- 偏差电量
        COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
        -- 结算电费
        COALESCE(SUM(pss.settlement_electric_fee), 0) as settlementElectricFee,
        -- 统计信息
        COUNT(*) as recordCount
    FROM power_side_settle pss
    INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
    WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
    GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
    ORDER BY month ASC
</select>

<!-- 获取电站月度交易信息（储能电站） -->
<select id="getStationMonthlyTradingInfoForStorage" resultType="java.util.Map">
    SELECT
        DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
        DATE_FORMAT(fsr.settle_date, '%m') as monthNumber,
        -- 期间类型（0-购电侧，1-售电侧）
        pss.data_type as dataType,
        CASE
            WHEN pss.data_type = 0 THEN '购电侧'
            WHEN pss.data_type = 1 THEN '售电侧'
            ELSE '未知'
        END as dataTypeName,
        -- 实际用电量
        COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
        -- 结算电量
        COALESCE(SUM(pss.settlement_electricity), 0) as settlementElectricity,
        -- 合同电量
        COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
        -- 偏差电量
        COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
        -- 结算电费
        COALESCE(SUM(pss.settlement_electric_fee), 0) as settlementElectricFee,
        -- 统计信息
        COUNT(*) as recordCount
    FROM power_side_settle pss
    INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
    WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
    GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m'), pss.data_type
    ORDER BY month ASC, pss.data_type ASC
</select>

<!-- 获取储能电站月度交易信息（按年份查询所有月份） -->
<select id="getStorageStationMonthlyTradingInfoByYear" resultType="java.util.Map">
    SELECT
        #{stationId} as stationId,
        #{year} as year,
        3 as stationType,
        COALESCE(
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'month', month,
                    'userSidePower', userSidePower,
                    'userSideFee', userSideFee,
                    'userSideAvgPrice', userSideAvgPrice,
                    'generationSidePower', generationSidePower,
                    'generationSideFee', generationSideFee,
                    'generationSideAvgPrice', generationSideAvgPrice
                )
            ),
            JSON_ARRAY()
        ) as monthlyData
    FROM (
        SELECT
            DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
            SUM(CASE WHEN pss.data_type = 1 THEN pss.settlement_electricity ELSE 0 END) as userSidePower,
            SUM(CASE WHEN pss.data_type = 1 THEN pss.settlement_electric_fee ELSE 0 END) as userSideFee,
            CASE
                WHEN SUM(CASE WHEN pss.data_type = 1 THEN pss.settlement_electricity ELSE 0 END) > 0
                THEN SUM(CASE WHEN pss.data_type = 1 THEN pss.settlement_electric_fee ELSE 0 END) /
                     SUM(CASE WHEN pss.data_type = 1 THEN pss.settlement_electricity ELSE 0 END)
                ELSE 0
            END as userSideAvgPrice,
            SUM(CASE WHEN pss.data_type = 2 THEN pss.settlement_electricity ELSE 0 END) as generationSidePower,
            SUM(CASE WHEN pss.data_type = 2 THEN pss.settlement_electric_fee ELSE 0 END) as generationSideFee,
            CASE
                WHEN SUM(CASE WHEN pss.data_type = 2 THEN pss.settlement_electricity ELSE 0 END) > 0
                THEN SUM(CASE WHEN pss.data_type = 2 THEN pss.settlement_electric_fee ELSE 0 END) /
                     SUM(CASE WHEN pss.data_type = 2 THEN pss.settlement_electricity ELSE 0 END)
                ELSE 0
            END as generationSideAvgPrice
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
            AND YEAR(fsr.settle_date) = #{year}
        GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
        ORDER BY month ASC
    ) monthly_data
</select>

<!-- 获取新能源电站月度交易信息（按年份查询所有月份） -->
<select id="getNewEnergyStationMonthlyTradingInfoByYear" resultType="java.util.Map">
    SELECT
        #{stationId} as stationId,
        #{year} as year,
        COALESCE(s.type, 1) as stationType,
        COALESCE(
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'month', month,
                    'totalPower', totalPower,
                    'totalFee', totalFee,
                    'avgPrice', avgPrice,
                    'midLongTermPower', midLongTermPower,
                    'midLongTermFee', midLongTermFee,
                    'guaranteePower', guaranteePower,
                    'guaranteeFee', guaranteeFee,
                    'deviationPower', deviationPower,
                    'deviationFee', deviationFee
                )
            ),
            JSON_ARRAY()
        ) as monthlyData
    FROM (
        SELECT
            DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
            SUM(pss.settlement_electricity) as totalPower,
            SUM(pss.settlement_electric_fee) as totalFee,
            CASE
                WHEN SUM(pss.settlement_electricity) > 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as avgPrice,
            SUM(CASE WHEN pss.settlement_type = 1 THEN pss.settlement_electricity ELSE 0 END) as midLongTermPower,
            SUM(CASE WHEN pss.settlement_type = 1 THEN pss.settlement_electric_fee ELSE 0 END) as midLongTermFee,
            SUM(CASE WHEN pss.settlement_type = 2 THEN pss.settlement_electricity ELSE 0 END) as guaranteePower,
            SUM(CASE WHEN pss.settlement_type = 2 THEN pss.settlement_electric_fee ELSE 0 END) as guaranteeFee,
            SUM(CASE WHEN pss.settlement_type IN (3, 4) THEN pss.settlement_electricity ELSE 0 END) as deviationPower,
            SUM(CASE WHEN pss.settlement_type IN (3, 4) THEN pss.settlement_electric_fee ELSE 0 END) as deviationFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
            AND YEAR(fsr.settle_date) = #{year}
        GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
        ORDER BY month ASC
    ) monthly_data
    LEFT JOIN station s ON s.id = #{stationId}
</select>

</mapper>