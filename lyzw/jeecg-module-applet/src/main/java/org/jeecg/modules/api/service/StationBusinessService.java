package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;

import java.util.List;
import java.util.Map;

/**
 * 电站业务服务接口
 * 
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
public interface StationBusinessService {

    /**
     * 获取电站列表（支持全国汇总）
     * 
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @param provinceId 省份ID (0-全国, 1-江苏, 2-安徽)
     * @param year 年份
     * @param month 月份
     * @param name 电站名称搜索
     * @return 电站列表分页数据
     */
    Map<String, Object> getStationList(Integer pageNo, Integer pageSize, Integer provinceId,
                                      String year, String month, String name);

    /**
     * 获取首页电站交易概况（新接口，符合需求规范）
     *
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @param provinceId 省份ID (0-全国, 1-安徽, 2-江苏)
     * @param dimension 查询维度 (1-月度, 2-年度)
     * @param year 年份
     * @param month 月份（月度查询时必填）
     * @param name 电站名称搜索
     * @return 电站交易概况分页数据，包含累计结算电量和交易均价
     */
    Map<String, Object> getStationTradingOverview(Integer pageNo, Integer pageSize, Integer provinceId,
                                                 Integer dimension, String year, String month, String name);

    /**
     * 获取全国汇总电站列表
     * 
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @param year 年份
     * @param month 月份
     * @param name 电站名称搜索
     * @return 全国汇总电站列表
     */
    Map<String, Object> getNationalStationList(Integer pageNo, Integer pageSize, 
                                              String year, String month, String name);

    /**
     * 获取单省份电站列表
     * 
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @param provinceId 省份ID
     * @param year 年份
     * @param month 月份
     * @param name 电站名称搜索
     * @return 单省份电站列表
     */
    Map<String, Object> getSingleProvinceStationList(Integer pageNo, Integer pageSize, Integer provinceId,
                                                    String year, String month, String name);

    /**
     * 获取电站详情
     * 
     * @param id 电站ID
     * @param provinceId 省份ID
     * @param date 日期
     * @param dimension 维度
     * @return 电站详情
     */
    StationDetailResponseDTO getStationDetail(Long id, String provinceId, String date, String dimension);

    /**
     * 获取电站年度交易电量信息
     * 
     * @param id 电站ID
     * @param provinceId 省份ID
     * @param year 年份
     * @return 年度交易电量信息
     */
    Map<String, Object> getStationYearlyTradingInfo(Long id, Integer provinceId, String year);

    /**
     * 获取全国汇总电站年度交易电量信息
     * 
     * @param id 电站ID
     * @param year 年份
     * @return 全国汇总年度交易电量信息
     */
    Map<String, Object> getNationalStationYearlyTradingInfo(Long id, String year);

    /**
     * 获取单省份电站年度交易电量信息
     * 
     * @param id 电站ID
     * @param provinceId 省份ID
     * @param year 年份
     * @return 单省份年度交易电量信息
     */
    Map<String, Object> getSingleProvinceStationYearlyTradingInfo(Long id, Integer provinceId, String year);

    /**
     * 获取电站月度交易电量信息
     * 
     * @param id 电站ID
     * @param provinceId 省份ID
     * @param yearMonth 年月
     * @return 月度交易电量信息
     */
    Map<String, Object> getStationMonthlyTradingInfo(Long id, Integer provinceId, String yearMonth);

    /**
     * 获取全国汇总电站月度交易电量信息
     * 
     * @param id 电站ID
     * @param yearMonth 年月
     * @return 全国汇总月度交易电量信息
     */
    Map<String, Object> getNationalStationMonthlyTradingInfo(Long id, String yearMonth);

    /**
     * 获取单省份电站月度交易电量信息
     * 
     * @param id 电站ID
     * @param provinceId 省份ID
     * @param yearMonth 年月
     * @return 单省份月度交易电量信息
     */
    Map<String, Object> getSingleProvinceStationMonthlyTradingInfo(Long id, Integer provinceId, String yearMonth);

    /**
     * 获取储能日清洁数据
     * 
     * @param provinceId 省份ID
     * @param stationId 电站ID
     * @param date 日期
     * @return 储能日清洁数据
     */
    List<EnergyStorageDailyClean> getEnergyStorageDailyClean(String provinceId, String stationId,
                                                           String date);


    /**
     * 获取单省份储能日清洁数据
     * 
     * @param provinceId 省份ID
     * @param stationId 电站ID
     * @param date 日期
     * @param dimension 维度
     * @return 单省份储能日清洁数据
     */
    List<EnergyStorageDailyClean> getSingleProvinceEnergyStorageDailyClean(String provinceId, String stationId,
                                                                          String date, String dimension);
}
