package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("energy_new_daily_clean")
public class EnergyNewDailyClean {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("date")
    @ApiModelProperty(value = "日期", example = "2023-01-01")
    private Date date;

    @TableField("station_id")
    @ApiModelProperty(value = "电站ID", example = "1001")
    private Long stationId;

    @TableField("mid_long_term_power")
    @ApiModelProperty(value = "中长期合约电量(单位:万千瓦时)", example = "100.12345")
    private BigDecimal midLongTermPower;

    @TableField("mid_long_term_price")
    @ApiModelProperty(value = "中长期单价(单位:元/千瓦时)", example = "0.5")
    private BigDecimal midLongTermPrice;

    @TableField("mid_long_term_fee")
    @ApiModelProperty(value = "中长期电费(单位:万元)", example = "50.12345")
    private BigDecimal midLongTermFee;

    @TableField("guarantee_power")
    @ApiModelProperty(value = "保障电量(单位:万千瓦时)", example = "80.12345")
    private BigDecimal guaranteePower;

    @TableField("guarantee_price")
    @ApiModelProperty(value = "保障电价(单位:元/千瓦时)", example = "0.4")
    private BigDecimal guaranteePrice;

    @TableField("guarantee_fee")
    @ApiModelProperty(value = "保障电费(单位:万元)", example = "32.12345")
    private BigDecimal guaranteeFee;

    @TableField("day_ahead_deviation_power")
    @ApiModelProperty(value = "日前偏差电量(单位:万千瓦时)", example = "10.12345")
    private BigDecimal dayAheadDeviationPower;

    @TableField("day_ahead_deviation_price")
    @ApiModelProperty(value = "日前偏差单价(单位:元/千瓦时)", example = "0.1")
    private BigDecimal dayAheadDeviationPrice;

    @TableField("day_ahead_deviation_fee")
    @ApiModelProperty(value = "日前偏差电费(单位:万元)", example = "1.12345")
    private BigDecimal dayAheadDeviationFee;

    @TableField("realtime_deviation_power")
    @ApiModelProperty(value = "实时偏差电量(单位:万千瓦时)", example = "5.12345")
    private BigDecimal realtimeDeviationPower;

    @TableField("realtime_deviation_price")
    @ApiModelProperty(value = "实时偏差单价(单位:元/千瓦时)", example = "0.05")
    private BigDecimal realtimeDeviationPrice;

    @TableField("realtime_deviation_fee")
    @ApiModelProperty(value = "实时偏差电费(单位:万元)", example = "0.25123")
    private BigDecimal realtimeDeviationFee;

    @TableField("excess_profit_recovery")
    @ApiModelProperty(value = "超额收益回收(单位:万元)", example = "2.12345")
    private BigDecimal excessProfitRecovery;

    @TableField("day_ahead_profit_recovery")
    @ApiModelProperty(value = "日前偏差收益回收(单位:万元)", example = "1.12345")
    private BigDecimal dayAheadProfitRecovery;

    @TableField("total_power")
    @ApiModelProperty(value = "上网电量(单位:万千瓦时)", example = "95.12345")
    private BigDecimal totalPower;

    @TableField("total_fee")
    @ApiModelProperty(value = "总电费(单位:万元)", example = "85.12345")
    private BigDecimal totalFee;

    @TableField("settlement_avg_price")
    @ApiModelProperty(value = "日结算均价(单位:元/千瓦时)", example = "0.6")
    private BigDecimal settlementAvgPrice;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;
}