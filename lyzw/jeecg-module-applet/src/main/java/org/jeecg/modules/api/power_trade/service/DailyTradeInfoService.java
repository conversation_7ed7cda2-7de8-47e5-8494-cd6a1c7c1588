package org.jeecg.modules.api.power_trade.service;

import org.jeecg.modules.api.power_trade.vo.DailyTradeInfoVO;

import java.util.List;

/**
 * 日交易信息服务接口
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
public interface DailyTradeInfoService {

    /**
     * 查询日交易信息
     * 
     * @param provinceId 省份ID (0-全国, 1-江苏, 2-安徽)
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param stationType 电站类型 (photovoltaic-光伏, wind-风电, storage-储能)
     * @return 日交易信息列表
     */
    List<DailyTradeInfoVO> getDailyTradeInfo(Integer provinceId, String startDate, String endDate, String stationType);

    /**
     * 查询单省份日交易信息
     * 
     * @param provinceId 省份ID (1-江苏, 2-安徽)
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param stationType 电站类型
     * @return 日交易信息列表
     */
    List<DailyTradeInfoVO> getProvinceDailyTradeInfo(Integer provinceId, String startDate, String endDate, String stationType);

    /**
     * 聚合全国日交易信息
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param stationType 电站类型
     * @return 聚合后的日交易信息列表
     */
    List<DailyTradeInfoVO> aggregateAllProvincesDailyTradeInfo(String startDate, String endDate, String stationType);

    /**
     * 查询安徽省新能源日交易信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param stationType 电站类型 (photovoltaic, wind)
     * @return 安徽省新能源日交易信息列表
     */
    List<DailyTradeInfoVO> getAnhuiNewEnergyDailyTradeInfo(String startDate, String endDate, String stationType);

    /**
     * 查询储能日交易信息
     * 
     * @param provinceId 省份ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 储能日交易信息列表
     */
    List<DailyTradeInfoVO> getStorageDailyTradeInfo(Integer provinceId, String startDate, String endDate);

    /**
     * 查询江苏省新能源日交易信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param stationType 电站类型 (photovoltaic, wind)
     * @return 江苏省新能源日交易信息列表
     */
    List<DailyTradeInfoVO> getJiangsuNewEnergyDailyTradeInfo(String startDate, String endDate, String stationType);
}
