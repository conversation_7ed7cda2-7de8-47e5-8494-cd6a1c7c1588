<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.ScreenMarketTradeMapper">

    <!-- 按年获取市场交易统计数据 -->
    <select id="getYearlyMarketTradeStats" resultType="org.jeecg.modules.api.power_trade.dto.YearlyMarketTradeDTO">
        SELECT
        type,
        year,
        SUM(IFNULL(power, 0)) as totalPower,
        CASE
        WHEN SUM(IFNULL(power, 0)) > 0 THEN
        SUM(IFNULL(power, 0) * IFNULL(price, 0)) / SUM(IFNULL(power, 0))
        ELSE 0
        END as avgPrice,
        CASE
        WHEN SUM(IFNULL(power, 0)) > 0 THEN
        SUM(IFNULL(power, 0) * IFNULL(discharge_price, 0)) / SUM(IFNULL(power, 0))
        ELSE 0
        END as avgDischargePrice,
        COUNT(*) as monthCount
        FROM screen_market_trade
        WHERE year = #{year}
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        GROUP BY type, year
        ORDER BY type, year
    </select>

    <!-- 获取所有年份的交易统计数据 -->
    <select id="getAllYearlyMarketTradeStats" resultType="org.jeecg.modules.api.power_trade.dto.YearlyMarketTradeDTO">
        SELECT
        type,
        year,
        SUM(IFNULL(power, 0)) as totalPower,
        CASE
        WHEN SUM(IFNULL(power, 0)) > 0 THEN
        SUM(IFNULL(power, 0) * IFNULL(price, 0)) / SUM(IFNULL(power, 0))
        ELSE 0
        END as avgPrice,
        CASE
        WHEN SUM(IFNULL(power, 0)) > 0 THEN
        SUM(IFNULL(power, 0) * IFNULL(discharge_price, 0)) / SUM(IFNULL(power, 0))
        ELSE 0
        END as avgDischargePrice,
        COUNT(*) as monthCount
        FROM screen_market_trade
        <where>
            <if test="type != null and type != ''">
                type = #{type}
            </if>
        </where>
        GROUP BY type, year
        ORDER BY type, year DESC
    </select>

    <!-- 按年份和类型获取月度明细数据 -->
    <select id="getMonthlyTradeDetails" resultType="org.jeecg.modules.api.power_trade.entity.ScreenMarketTrade">
        SELECT
        type,
        year,
        month,
        power,
        price,
        discharge_price as dischargePrice
        FROM screen_market_trade
        WHERE year = #{year}
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        ORDER BY type, month
    </select>

</mapper>