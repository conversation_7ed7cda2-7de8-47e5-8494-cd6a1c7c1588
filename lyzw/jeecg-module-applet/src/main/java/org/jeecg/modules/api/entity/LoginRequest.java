package org.jeecg.modules.api.power_trade.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录请求参数
 */
@Data
@ApiModel(value = "LoginRequest", description = "工号登录请求参数")
public class LoginRequest {

    @ApiModelProperty(value = "工号", required = true)
    private String personNumber;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

}
