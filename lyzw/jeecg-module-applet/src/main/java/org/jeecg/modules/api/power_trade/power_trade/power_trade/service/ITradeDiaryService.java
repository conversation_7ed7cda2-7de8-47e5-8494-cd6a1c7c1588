package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.TradeDiary;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 交易日历服务接口
 */
public interface ITradeDiaryService extends IService<TradeDiary> {

    List<TradeDiary> getTradeDiaryByDateRange(Date startDate, Date endDate, List<Integer> provinceIds);

    Map<String, Object> getTradeCalendarByProvinceWithPage(Integer provinceId, int pageNo, int pageSize);
}