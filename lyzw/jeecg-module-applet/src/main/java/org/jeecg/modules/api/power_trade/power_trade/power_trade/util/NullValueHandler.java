package org.jeecg.modules.api.power_trade.util;

import java.math.BigDecimal;
import java.util.*;

/**
 * 空值处理工具类
 * 确保所有接口返回值不为空，空值统一设置为0或默认值
 */
public class NullValueHandler {

    /**
     * 确保Map中的所有数值字段不为空，空值设置为0
     */
    public static Map<String, Object> ensureNoNullValues(Map<String, Object> data) {
        if (data == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> result = new HashMap<>(data);
        
        // 处理常见的BigDecimal数值字段
        String[] bigDecimalFields = {
            "current_month_power", "current_month_plan_power", "settlement_average_price",
            "totalPower", "totalFee", "settlementAvgPrice", "midLongTermPower", "guaranteePower",
            "yearlyTotalPower", "yearlyAvgPrice", "monthlyTotalPower", "monthlyAvgPrice",
            "capacity", "longitude", "latitude", "power", "generation", "price", "fee",
            "currentMonthPower", "currentMonthPlanPower", "settlementAveragePrice",
            "currentPeriodGeneration", "totalGeneration", "avgPrice", "planValue"
        };
        
        for (String field : bigDecimalFields) {
            if (result.containsKey(field) && result.get(field) == null) {
                result.put(field, BigDecimal.ZERO);
            }
        }
        
        // 处理Long类型字段
        String[] longFields = {
            "id", "stationId", "factoryId", "typeId", "total"
        };
        
        for (String field : longFields) {
            if (result.containsKey(field) && result.get(field) == null) {
                result.put(field, 0L);
            }
        }
        
        // 处理Integer类型字段
        String[] integerFields = {
            "type", "tradeStatus", "powerStatus", "dayAheadDeclarationStatus", 
            "sortNo", "provinceId", "count", "size", "pages", "current", "pageNo", "pageSize",
            "dimension", "status", "level", "eventCount"
        };
        
        for (String field : integerFields) {
            if (result.containsKey(field) && result.get(field) == null) {
                result.put(field, 0);
            }
        }
        
        // 处理Double类型字段
        String[] doubleFields = {
            "generation", "consumption", "efficiency", "rate", "percentage"
        };
        
        for (String field : doubleFields) {
            if (result.containsKey(field) && result.get(field) == null) {
                result.put(field, 0.0);
            }
        }
        
        // 处理字符串字段
        String[] stringFields = {
            "name", "dispatchName", "tradeType", "dataSource", "remark", "message",
            "factoryName", "typeName", "description", "label", "title", "content",
            "year", "month", "date", "time", "startTime", "endTime"
        };
        
        for (String field : stringFields) {
            if (result.containsKey(field) && result.get(field) == null) {
                result.put(field, "");
            }
        }
        
        return result;
    }

    /**
     * 确保List中的所有Map都不包含空值
     */
    public static List<Map<String, Object>> ensureListNoNullValues(List<Map<String, Object>> dataList) {
        if (dataList == null) {
            return new ArrayList<>();
        }
        
        return dataList.stream()
                .map(NullValueHandler::ensureNoNullValues)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 确保BigDecimal不为空
     */
    public static BigDecimal ensureNotNull(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 确保Integer不为空
     */
    public static Integer ensureNotNull(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * 确保Long不为空
     */
    public static Long ensureNotNull(Long value) {
        return value != null ? value : 0L;
    }

    /**
     * 确保String不为空
     */
    public static String ensureNotNull(String value) {
        return value != null ? value : "";
    }

    /**
     * 确保Double不为空
     */
    public static Double ensureNotNull(Double value) {
        return value != null ? value : 0.0;
    }

    /**
     * 确保List不为空
     */
    public static <T> List<T> ensureNotNull(List<T> list) {
        return list != null ? list : new ArrayList<>();
    }

    /**
     * 添加默认结算数据
     */
    public static void addDefaultSettlementData(Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        data.put("current_month_power", BigDecimal.ZERO);
        data.put("current_month_plan_power", BigDecimal.ZERO);
        data.put("settlement_average_price", BigDecimal.ZERO);
        data.put("totalPower", BigDecimal.ZERO);
        data.put("totalFee", BigDecimal.ZERO);
        data.put("settlementAvgPrice", BigDecimal.ZERO);
        data.put("midLongTermPower", BigDecimal.ZERO);
        data.put("guaranteePower", BigDecimal.ZERO);
        data.put("yearlyTotalPower", BigDecimal.ZERO);
        data.put("yearlyAvgPrice", BigDecimal.ZERO);
        data.put("monthlyTotalPower", BigDecimal.ZERO);
        data.put("monthlyAvgPrice", BigDecimal.ZERO);
    }

    /**
     * 添加默认发电数据
     */
    public static void addDefaultPowerGenerationData(Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        data.put("currentPeriodGeneration", BigDecimal.ZERO);
        data.put("totalGeneration", BigDecimal.ZERO);
        data.put("generation", 0.0);
        data.put("consumption", 0.0);
        data.put("efficiency", 0.0);
        data.put("detailData", new ArrayList<>());
    }

    /**
     * 添加默认交易数据
     */
    public static void addDefaultTradeData(Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        data.put("dataSource", "");
        data.put("photovoltaicWind", createDefaultTradeInfo());
        data.put("energyStorage", createDefaultTradeInfo());
    }

    /**
     * 创建默认交易信息
     */
    private static Map<String, Object> createDefaultTradeInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("totalPower", BigDecimal.ZERO);
        info.put("totalFee", BigDecimal.ZERO);
        info.put("settlementAvgPrice", BigDecimal.ZERO);
        info.put("midLongTermPower", BigDecimal.ZERO);
        info.put("guaranteePower", BigDecimal.ZERO);
        info.put("yearlyTotalPower", BigDecimal.ZERO);
        info.put("yearlyAvgPrice", BigDecimal.ZERO);
        info.put("monthlyTotalPower", BigDecimal.ZERO);
        info.put("monthlyAvgPrice", BigDecimal.ZERO);
        return info;
    }

    /**
     * 添加默认分页数据
     */
    public static void addDefaultPaginationData(Map<String, Object> data) {
        if (data == null) {
            return;
        }
        
        data.put("current", 1);
        data.put("size", 10);
        data.put("total", 0L);
        data.put("pages", 0);
        data.put("records", new ArrayList<>());
    }

    /**
     * 确保分页结果不为空
     */
    public static Map<String, Object> ensurePaginationResult(Map<String, Object> result) {
        if (result == null) {
            result = new HashMap<>();
            addDefaultPaginationData(result);
        } else {
            result = ensureNoNullValues(result);
            if (!result.containsKey("records")) {
                result.put("records", new ArrayList<>());
            }
        }
        return result;
    }
}
