package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;

import java.util.List;

public interface TradeDiaryFileMapper extends BaseMapper<TradeDiaryFile> {
    
    /**
     * 根据交易日历ID查询附件列表
     * @param tradeDiaryId 交易日历ID
     * @return 附件列表
     */
    List<TradeDiaryFile> selectFilesByDiaryId(@Param("tradeDiaryId") Long tradeDiaryId);

    /**
     * 批量查询交易日历附件
     * @param tradeDiaryIds 交易日历ID列表
     * @return 附件列表
     */
    List<TradeDiaryFile> selectFilesByDiaryIds(@Param("tradeDiaryIds") List<Long> tradeDiaryIds);
} 

