package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;

import java.util.List;
import java.util.Map;

/**
 * 交易日历附件服务接口
 */
public interface ITradeDiaryFileService extends IService<TradeDiaryFile> {


    List<TradeDiaryFile> getFilesByDiaryId(Long tradeDiaryId);

    /**
     * 批量查询交易日历附件
     * @param tradeDiaryIds 交易日历ID列表
     * @return 按交易日历ID分组的附件列表
     */
    Map<Long, List<TradeDiaryFile>> getFilesByDiaryIds(List<Long> tradeDiaryIds);
} 