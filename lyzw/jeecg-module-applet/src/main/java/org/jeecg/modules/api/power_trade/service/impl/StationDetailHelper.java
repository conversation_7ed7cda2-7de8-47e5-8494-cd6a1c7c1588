package org.jeecg.modules.api.power_trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power.mapper.JiaYueRpMapper;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power.entity.RpJiaYue;
import org.jeecg.modules.api.power_trade.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 电站详情构建辅助类
 * 
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
@Slf4j
@Component
public class StationDetailHelper {

    @Autowired
    private JiaYueRpMapper jiaYueRpMapper;

    @Autowired
    private PowerSideSettleMapper powerSideSettleMapper;
    private StationService stationService;


    /**
     * 计算累计发电量 - 基于实际功率数据
     */
    public BigDecimal calculateTotalPowerGeneration(Long stationId, String year) {
        try {
            log.info("开始计算电站{}年度累计发电量 - 年份: {}", stationId, year);

            // 构建年度时间范围
            String startDate = year + "-01-01";
            String endDate = year + "-12-31";

            // 查询年度实际功率数据
            List<String> stationNos = Arrays.asList(String.valueOf(stationId));
            List<RpJiaYue> powerData = jiaYueRpMapper.selectMaxVersionByDateRangeAndStation(
                    startDate, endDate, stationNos, 1); // 1表示瞬时值

            if (powerData == null || powerData.isEmpty()) {
                log.warn("未查询到电站{}的年度功率数据", stationId);
                return BigDecimal.ZERO;
            }

            // 计算总发电量：与PowerService.java保持一致，实际功率 ÷ 4 = 发电量
            BigDecimal totalGeneration = powerData.stream()
                    .filter(rp -> rp.getValue() != null && rp.getValue() > 0)
                    .map(rp -> BigDecimal.valueOf(rp.getValue() / 4.0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            log.info("电站{}年度累计发电量计算完成 - 数据点数: {}, 总发电量: {} MWh",
                    stationId, powerData.size(), totalGeneration);

            return totalGeneration;

        } catch (Exception e) {
            log.error("计算累计发电量失败 - 电站ID: {}", stationId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取结算数据 - 从结算表中获取实际数据
     */
    public Map<String, Object> getSettlementData(Object station, String year, String month) {
        Map<String, Object> settlementData = new HashMap<>();

        try {
            log.info("开始获取结算数据 - 年份: {}, 月份: {}", year, month);

            // 获取电站ID
            Long stationId = null;
            if (station instanceof Map) {
                stationId = ((Map<String, Object>) station).get("id") instanceof Long ?
                        (Long) ((Map<String, Object>) station).get("id") : null;
            } else {
                // 尝试通过反射获取ID
                try {
                    stationId = (Long) station.getClass().getMethod("getId").invoke(station);
                } catch (Exception e) {
                    log.warn("无法从station对象获取ID", e);
                }
            }

            if (stationId == null) {
                log.warn("无法获取电站ID，返回默认结算数据");
                settlementData.put("current_month_power", BigDecimal.ZERO);
                settlementData.put("current_month_plan_power", BigDecimal.ZERO);
                settlementData.put("settlement_average_price", BigDecimal.ZERO);
                return settlementData;
            }

            // 构建月份查询参数
            String yearMonth = year + "-" + month;

            // 查询月度结算数据
            Map<String, Object> monthlyData = powerSideSettleMapper.getStationMonthlyTradingInfo(stationId, yearMonth);

            if (monthlyData != null && !monthlyData.isEmpty()) {
                // 从结算数据中提取所需字段
                BigDecimal currentMonthPower = (BigDecimal) monthlyData.getOrDefault("monthlyElectricity", BigDecimal.ZERO);
                BigDecimal currentMonthPlanPower = (BigDecimal) monthlyData.getOrDefault("monthlyPlanElectricity", BigDecimal.ZERO);
                BigDecimal settlementAvgPrice = (BigDecimal) monthlyData.getOrDefault("monthlyAveragePrice", BigDecimal.ZERO);

                settlementData.put("current_month_power", currentMonthPower);
                settlementData.put("current_month_plan_power", currentMonthPlanPower);
                settlementData.put("settlement_average_price", settlementAvgPrice);

                log.info("结算数据获取成功 - 电站ID: {}, 月度电量: {}, 计划电量: {}, 结算均价: {}",
                        stationId, currentMonthPower, currentMonthPlanPower, settlementAvgPrice);
            } else {
                log.warn("未查询到电站{}的月度结算数据 - 年月: {}", stationId, yearMonth);
                settlementData.put("current_month_power", BigDecimal.ZERO);
                settlementData.put("current_month_plan_power", BigDecimal.ZERO);
                settlementData.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.error("获取结算数据失败", e);
            settlementData.put("current_month_power", BigDecimal.ZERO);
            settlementData.put("current_month_plan_power", BigDecimal.ZERO);
            settlementData.put("settlement_average_price", BigDecimal.ZERO);
        }

        return settlementData;
    }

    /**
     * 获取省份名称
     */
    public String getProvinceName(Integer provinceId) {
        if (provinceId == null) return "未知省份";
        switch (provinceId) {
            case 1: return "安徽省";
            case 2: return "江苏省";
            default: return "未知省份";
        }
    }

    /**
     * 构建默认电站详情响应 - 简化版本，只包含基础信息和发电量
     */
    public StationDetailResponseDTO buildDefaultStationDetailResponse(Long stationId, String date, String dimension) {
        StationDetailResponseDTO response = new StationDetailResponseDTO();
        response.setBasicInfo(buildDefaultBasicInfo(stationId));
        response.setPowerGeneration(buildDefaultPowerGenerationInfo(dimension, date));
        return response;
    }

    /**
     * 构建默认基础信息
     */
    public StationDetailResponseDTO.BasicInfo buildDefaultBasicInfo(Long stationId) {
        StationDetailResponseDTO.BasicInfo basicInfo = new StationDetailResponseDTO.BasicInfo();
        basicInfo.setStation(stationService.getById(stationId));
        basicInfo.setYearPlanPowerGeneration(BigDecimal.ZERO);
        basicInfo.setTotalPowerGeneration(BigDecimal.ZERO);
        basicInfo.setCurrentMonthPower(BigDecimal.ZERO);
        basicInfo.setCurrentMonthPlanPower(BigDecimal.ZERO);
        basicInfo.setSettlementAveragePrice(BigDecimal.ZERO);
        return basicInfo;
    }

    /**
     * 构建默认发电量信息 - 确保返回正确数量的0值数据
     */
    public StationDetailResponseDTO.PowerGenerationInfo buildDefaultPowerGenerationInfo(String dimension, String date) {
        StationDetailResponseDTO.PowerGenerationInfo powerGeneration = new StationDetailResponseDTO.PowerGenerationInfo();
        powerGeneration.setCurrentPeriodGeneration(BigDecimal.ZERO);

        // 根据维度生成对应数量的0值数据
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = generateZeroDataByDimension(dimension, date);
        powerGeneration.setDetailData(detailData);

        powerGeneration.setDimension(dimension);
        powerGeneration.setQueryDate(date);
        return powerGeneration;
    }

    /**
     * 根据维度生成对应数量的0值数据
     */
    private List<StationDetailResponseDTO.PowerGenerationDetailData> generateZeroDataByDimension(String dimension, String date) {
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();

        switch (dimension) {
            case "1": // 年维度 - 返回12个月的0值数据
                String year = date.length() >= 4 ? date.substring(0, 4) : "2024";
                for (int month = 1; month <= 12; month++) {
                    String monthStr = String.format("%02d", month);
                    StationDetailResponseDTO.PowerGenerationDetailData detail =
                            new StationDetailResponseDTO.PowerGenerationDetailData();
                    detail.setTimeLabel(year + "-" + monthStr);
                    detail.setActualPower(0.0);
                    detail.setGeneration(0.0);
                    detailData.add(detail);
                }
                break;

            case "2": // 月维度 - 返回该月天数的0值数据
                String yearMonth = date.length() >= 4 ? date.substring(0, 4) : "2024";
                String monthPart = date.length() >= 7 ? date.substring(5, 7) : "01";
                int daysInMonth = getDaysInMonth(yearMonth, monthPart);

                for (int day = 1; day <= daysInMonth; day++) {
                    String dayStr = String.format("%02d", day);
                    StationDetailResponseDTO.PowerGenerationDetailData detail =
                            new StationDetailResponseDTO.PowerGenerationDetailData();
                    detail.setTimeLabel(monthPart + "-" + dayStr);
                    detail.setActualPower(0.0);
                    detail.setGeneration(0.0);
                    detailData.add(detail);
                }
                break;

            case "3": // 日维度 - 返回96个时间点的0值数据
                for (int hour = 0; hour < 24; hour++) {
                    for (int minute = 0; minute < 60; minute += 15) {
                        String timePoint = String.format("%02d:%02d", hour, minute);
                        StationDetailResponseDTO.PowerGenerationDetailData detail =
                                new StationDetailResponseDTO.PowerGenerationDetailData();
                        detail.setTimeLabel(timePoint);
                        detail.setActualPower(0.0);
                        detail.setGeneration(0.0);
                        detailData.add(detail);
                    }
                }
                break;

            default:
                // 不支持的维度，返回空数据
                break;
        }

        return detailData;
    }

    /**
     * 计算指定年月的天数
     */
    private int getDaysInMonth(String year, String month) {
        try {
            int y = Integer.parseInt(year);
            int m = Integer.parseInt(month);

            // 使用Java 8的LocalDate来计算天数
            return java.time.LocalDate.of(y, m, 1).lengthOfMonth();
        } catch (Exception e) {
            // 如果解析失败，返回默认值
            return 30;
        }
    }

}
