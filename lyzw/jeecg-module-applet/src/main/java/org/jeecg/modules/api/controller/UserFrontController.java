package org.jeecg.modules.api.power_trade.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.LocalCacheUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.api.power_trade.entity.UserFront;
import org.jeecg.modules.api.power_trade.service.UserFrontService;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;

@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/power_trade/user")
@Slf4j
public class UserFrontController {

    @Autowired
    private UserFrontService userFrontService;

    @Autowired
    private LocalCacheUtil localCacheUtil;

    @ApiOperation(value = "工号登录")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public Result<?> login(@RequestBody UserFront loginUser) {
        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validateRequired(loginUser.getPersonNumber(), "工号")
                    .validateRequired(loginUser.getPassword(), "密码");
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        String password = loginUser.getPassword();

        // 用户验证逻辑
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPersonNumber, loginUser.getPersonNumber()));
        if (front == null) {
            return Result.error(500, "用户名或密码错误");
        }

        // 密码验证
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String storedPassword = front.getPassword(); // 数据库中存储的密码

        boolean passwordMatches = passwordEncoder.matches(password, storedPassword);
        if (!passwordMatches) {
            return Result.error(500, "用户名或密码错误");
        }

        // 账户状态检查 - 使用安全的比较方式
        if (!Integer.valueOf(1).equals(front.getStatus())) {
            return Result.error(500, "当前账号已被禁用,请联系管理员");
        }

        // 微信状态检查 - 使用安全的比较方式，null或0都不允许访问
        if (!Integer.valueOf(1).equals(front.getWechatStatus())) {
            return Result.error(500, "当前账号不被允许访问,请联系管理员");
        }
        JSONObject obj = new JSONObject(new LinkedHashMap<>());
        String token = JwtUtil.sign(front.getPersonNumber(), front.getPassword());
        localCacheUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        localCacheUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        obj.put("token", token);
        obj.put("userInfo", front);

        return Result.OK("登录成功!", obj);
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation(value = "获取当前用户信息")
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.GET)
    public Result<?> getUserInfo(HttpServletRequest request) {
        String userIdByToken = CommonUtils.getUserIdByToken();
        UserFront user = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getId, userIdByToken));
        return Result.OK(user);
    }

    /**
     * 用户退出
     */
    @ApiOperation(value = "客户端 - 用户退出")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public Result<?> logout(HttpServletRequest request) {
        String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
        if (oConvertUtils.isEmpty(token)) {
            return Result.error("未登录！");
        }
        localCacheUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
        return Result.OK("退出成功！");
    }
}
