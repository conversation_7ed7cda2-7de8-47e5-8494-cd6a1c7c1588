package org.jeecg.modules.api.power_trade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间范围DTO
 * 用于封装查询的时间范围参数
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeRange {
    
    /**
     * 开始日期 (yyyy-MM-dd)
     */
    private String startDate;
    
    /**
     * 结束日期 (yyyy-MM-dd)
     */
    private String endDate;
    
    /**
     * 开始年月 (yyyy-MM)
     */
    private String startYearMonth;
    
    /**
     * 结束年月 (yyyy-MM)
     */
    private String endYearMonth;
    
    /**
     * 获取时间范围描述
     */
    public String getDescription() {
        if (startYearMonth != null && endYearMonth != null) {
            if (startYearMonth.equals(endYearMonth)) {
                return "月度查询: " + startYearMonth;
            } else {
                return "年度查询: " + startYearMonth + " 至 " + endYearMonth;
            }
        } else if (startDate != null && endDate != null) {
            if (startDate.equals(endDate)) {
                return "日度查询: " + startDate;
            } else {
                return "日期范围查询: " + startDate + " 至 " + endDate;
            }
        }
        return "未知时间范围";
    }
    
    /**
     * 判断是否为月度查询
     */
    public boolean isMonthlyQuery() {
        return startYearMonth != null && endYearMonth != null && startYearMonth.equals(endYearMonth);
    }
    
    /**
     * 判断是否为年度查询
     */
    public boolean isYearlyQuery() {
        return startYearMonth != null && endYearMonth != null && !startYearMonth.equals(endYearMonth);
    }
    
    /**
     * 判断是否为日度查询
     */
    public boolean isDailyQuery() {
        return startDate != null && endDate != null && startDate.equals(endDate);
    }
}
