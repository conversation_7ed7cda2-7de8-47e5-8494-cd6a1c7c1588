package org.jeecg.modules.api.power.service;

import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerQueryParam;

import java.util.List;

/**
 * 功率预测策略接口
 */
public interface PowerStrategy {
    
    /**
     * 获取支持的厂家ID
     * @return 厂家ID
     */
    default Integer getSupportedFactoryId() {
        return 0; // 默认厂家ID
    }
    
    /**
     * 根据时间范围查询功率预测
     * @param param 查询参数
     * @return 功率预测数据列表
     */
    List<CommonPowerDto> queryPowerPredictionByRange(PowerQueryParam param);
    
    /**
     * 根据日期列表查询功率预测
     * @param param 查询参数
     * @return 功率预测数据列表
     */
    List<CommonPowerDto> queryPowerPredictionByDateList(PowerQueryParam param);
    
    /**
     * 根据时间范围查询实际功率
     * @param param 查询参数
     * @return 实际功率数据列表
     */
    List<CommonPowerDto> queryPowerRealByRange(PowerQueryParam param);
    
    /**
     * 根据日期列表查询实际功率
     * @param param 查询参数
     * @return 实际功率数据列表
     */
    List<CommonPowerDto> queryPowerRealByRangeDateList(PowerQueryParam param);
    
    /**
     * 查询最大版本号
     * @param param 查询参数
     * @return 最大版本号
     */
    String queryMaxVersion(PowerQueryParam param);
}
