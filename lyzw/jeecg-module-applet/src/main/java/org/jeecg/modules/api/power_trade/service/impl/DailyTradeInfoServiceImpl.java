package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyNewDailyCleanMapper;
import org.jeecg.modules.api.power_trade.mapper.EnergyStorageDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.DailyTradeInfoService;
import org.jeecg.modules.api.power_trade.vo.DailyTradeInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 日交易信息服务实现类
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Service
@Slf4j
public class DailyTradeInfoServiceImpl implements DailyTradeInfoService {

    private final EnergyNewDailyCleanMapper energyNewDailyCleanMapper;
    private final EnergyStorageDailyCleanMapper energyStorageDailyCleanMapper;

    private static final int QUERY_TIMEOUT_SECONDS = 30;
    private static final String STORAGE_TYPE = "storage";
    private static final String PHOTOVOLTAIC_TYPE = "photovoltaic";
    private static final String WIND_TYPE = "wind";
    private static final int JIANGSU_PROVINCE_ID = 1;
    private static final int ANHUI_PROVINCE_ID = 2;
    private static final String JIANGSU_PROVINCE_NAME = "江苏";
    private static final String ANHUI_PROVINCE_NAME = "安徽";

    public DailyTradeInfoServiceImpl(EnergyNewDailyCleanMapper energyNewDailyCleanMapper,
                                     EnergyStorageDailyCleanMapper energyStorageDailyCleanMapper) {
        this.energyNewDailyCleanMapper = energyNewDailyCleanMapper;
        this.energyStorageDailyCleanMapper = energyStorageDailyCleanMapper;
    }

    @Override
    public List<DailyTradeInfoVO> getDailyTradeInfo(Integer provinceId, String startDate, String endDate, String stationType) {
        log.info("开始查询日交易信息 - 省份ID: {}, 开始日期: {}, 结束日期: {}, 电站类型: {}",
                provinceId, startDate, endDate, stationType);

        return executeWithExceptionHandling(() -> {
            if (provinceId == 0) {
                return aggregateAllProvincesDailyTradeInfo(startDate, endDate, stationType);
            } else {
                return getProvinceDailyTradeInfo(provinceId, startDate, endDate, stationType);
            }
        }, "查询日交易信息失败");
    }

    @Override
    public List<DailyTradeInfoVO> getProvinceDailyTradeInfo(Integer provinceId, String startDate, String endDate, String stationType) {
        log.info("查询单省份日交易信息 - 省份ID: {}, 开始日期: {}, 结束日期: {}, 电站类型: {}",
                provinceId, startDate, endDate, stationType);

        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("不支持的省份ID: {}", provinceId);
            return Collections.emptyList();
        }

        return executeWithDataSource(dsKey, () -> {
            List<DailyTradeInfoVO> result = StringUtils.hasText(stationType)
                ? querySpecificStationType(provinceId, startDate, endDate, stationType.trim())
                : queryAllStationTypes(provinceId, startDate, endDate);

            log.info("单省份查询完成 - 省份ID: {}, 返回记录数: {}", provinceId, result.size());
            return result;
        });
    }

    @Override
    public List<DailyTradeInfoVO> aggregateAllProvincesDailyTradeInfo(String startDate, String endDate, String stationType) {
        log.info("开始聚合全国日交易信息 - 开始日期: {}, 结束日期: {}, 电站类型: {}", startDate, endDate, stationType);

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();
        List<CompletableFuture<List<DailyTradeInfoVO>>> futures = new ArrayList<>();

        // 并行查询各省份数据
        for (Map.Entry<Integer, String> entry : allDataSources.entrySet()) {
            Integer provinceId = entry.getKey();
            if (provinceId == 0) continue; // 跳过全国汇总

            CompletableFuture<List<DailyTradeInfoVO>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return getProvinceDailyTradeInfo(provinceId, startDate, endDate, stationType);
                } catch (Exception e) {
                    log.warn("查询省份{}的日交易信息失败: {}", provinceId, e.getMessage());
                    return new ArrayList<>();
                }
            });
            futures.add(future);
        }

        // 收集所有结果
        List<DailyTradeInfoVO> allResults = new ArrayList<>();
        try {
            for (CompletableFuture<List<DailyTradeInfoVO>> future : futures) {
                List<DailyTradeInfoVO> provinceResult = future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                if (provinceResult != null && !provinceResult.isEmpty()) {
                    allResults.addAll(provinceResult);
                }
            }
        } catch (Exception e) {
            log.error("聚合全国日交易信息失败", e);
        }

        // 按日期排序
        allResults.sort(Comparator.comparing(DailyTradeInfoVO::getDate));

        log.info("全国数据聚合完成 - 返回记录数: {}", allResults.size());
        return allResults;
    }

    @Override
    public List<DailyTradeInfoVO> getAnhuiNewEnergyDailyTradeInfo(String startDate, String endDate, String stationType) {
        log.info("查询安徽省新能源日交易信息 - 开始日期: {}, 结束日期: {}, 电站类型: {}", startDate, endDate, stationType);

        List<DailyTradeInfoVO> result = new ArrayList<>();
        
        try {
            // 查询energy_new_daily_clean表
            List<EnergyNewDailyClean> energyData = energyNewDailyCleanMapper.selectByDateRange(startDate, endDate, stationType);
            
            for (EnergyNewDailyClean data : energyData) {
                DailyTradeInfoVO vo = convertEnergyNewDailyCleanToVO(data);
                vo.setProvinceId(2); // 安徽省
                vo.setProvinceName("安徽");
                result.add(vo);
            }

            log.info("安徽省新能源数据查询完成 - 返回记录数: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询安徽省新能源日交易信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DailyTradeInfoVO> getStorageDailyTradeInfo(Integer provinceId, String startDate, String endDate) {
        log.info("查询储能日交易信息 - 省份ID: {}, 开始日期: {}, 结束日期: {}", provinceId, startDate, endDate);

        // 江苏省不支持储能数据
        if (provinceId == 1) {
            log.info("江苏省不支持储能数据查询");
            return new ArrayList<>();
        }

        List<DailyTradeInfoVO> result = new ArrayList<>();
        
        try {
            // 查询energy_storage_daily_clean表
            List<EnergyStorageDailyClean> storageData = energyStorageDailyCleanMapper.selectByDateRange(startDate, endDate);
            
            for (EnergyStorageDailyClean data : storageData) {
                DailyTradeInfoVO vo = convertEnergyStorageDailyCleanToVO(data);
                vo.setProvinceId(provinceId);
                vo.setProvinceName(ProvinceDataSourceUtil.getProvinceName(provinceId));
                vo.setStationType("storage");
                result.add(vo);
            }

            log.info("储能数据查询完成 - 省份ID: {}, 返回记录数: {}", provinceId, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询储能日交易信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DailyTradeInfoVO> getJiangsuNewEnergyDailyTradeInfo(String startDate, String endDate, String stationType) {
        log.info("查询江苏省新能源日交易信息 - 开始日期: {}, 结束日期: {}, 电站类型: {}", startDate, endDate, stationType);

        List<DailyTradeInfoVO> result = new ArrayList<>();
        
        try {
            // 查询energy_new_daily_clean表
            List<EnergyNewDailyClean> energyData = energyNewDailyCleanMapper.selectByDateRange(startDate, endDate, stationType);
            
            for (EnergyNewDailyClean data : energyData) {
                DailyTradeInfoVO vo = convertEnergyNewDailyCleanToVO(data);
                vo.setProvinceId(1); // 江苏省
                vo.setProvinceName("江苏");
                result.add(vo);
            }

            log.info("江苏省新能源数据查询完成 - 返回记录数: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询江苏省新能源日交易信息失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询所有电站类型的数据
     */
    private List<DailyTradeInfoVO> queryAllStationTypes(Integer provinceId, String startDate, String endDate) {
        List<DailyTradeInfoVO> result = new ArrayList<>();

        // 查询新能源数据（光伏和风电）
        result.addAll(queryNewEnergyData(provinceId, startDate, endDate, null));

        // 查询储能数据（江苏省除外）
        if (provinceId != 1) {
            result.addAll(getStorageDailyTradeInfo(provinceId, startDate, endDate));
        }

        return result;
    }

    /**
     * 查询指定电站类型的数据
     */
    private List<DailyTradeInfoVO> querySpecificStationType(Integer provinceId, String startDate, String endDate, String stationType) {
        if ("storage".equals(stationType)) {
            return getStorageDailyTradeInfo(provinceId, startDate, endDate);
        } else if ("photovoltaic".equals(stationType) || "wind".equals(stationType)) {
            return queryNewEnergyData(provinceId, startDate, endDate, stationType);
        } else {
            log.warn("不支持的电站类型: {}", stationType);
            return new ArrayList<>();
        }
    }

    /**
     * 查询新能源数据
     */
    private List<DailyTradeInfoVO> queryNewEnergyData(Integer provinceId, String startDate, String endDate, String stationType) {
        if (provinceId == 1) {
            return getJiangsuNewEnergyDailyTradeInfo(startDate, endDate, stationType);
        } else if (provinceId == 2) {
            return getAnhuiNewEnergyDailyTradeInfo(startDate, endDate, stationType);
        } else {
            log.warn("不支持的省份ID: {}", provinceId);
            return new ArrayList<>();
        }
    }

    /**
     * 将EnergyNewDailyClean实体转换为VO
     */
    private DailyTradeInfoVO convertEnergyNewDailyCleanToVO(EnergyNewDailyClean data) {
        DailyTradeInfoVO vo = new DailyTradeInfoVO();

        // 基础信息
        if (data.getDate() != null) {
            vo.setDate(data.getDate().toString());
        }
        vo.setStationId(data.getStationId());

        // 根据电站类型设置stationType，这里需要根据实际业务逻辑确定
        // 暂时设置为photovoltaic，实际应该根据station表的type字段判断
        vo.setStationType("photovoltaic");

        // 安徽省光伏/风电字段映射
        vo.setMidLongTermPower(data.getMidLongTermPower());
        vo.setMidLongTermPrice(data.getMidLongTermPrice());
        vo.setMidLongTermFee(data.getMidLongTermFee());
        vo.setGuaranteePower(data.getGuaranteePower());
        vo.setGuaranteePrice(data.getGuaranteePrice());
        vo.setGuaranteeFee(data.getGuaranteeFee());
        vo.setDayAheadDeviationPower(data.getDayAheadDeviationPower());
        vo.setDayAheadDeviationPrice(data.getDayAheadDeviationPrice());
        vo.setDayAheadDeviationFee(data.getDayAheadDeviationFee());
        vo.setRealtimeDeviationPower(data.getRealtimeDeviationPower());
        vo.setRealtimeDeviationPrice(data.getRealtimeDeviationPrice());
        vo.setRealtimeDeviationFee(data.getRealtimeDeviationFee());
        vo.setExcessProfitRecovery(data.getExcessProfitRecovery());
        vo.setDayAheadProfitRecovery(data.getDayAheadProfitRecovery());
        vo.setTotalPower(data.getTotalPower());
        vo.setTotalFee(data.getTotalFee());
        vo.setSettlementAvgPrice(data.getSettlementAvgPrice());

        return vo;
    }

    /**
     * 将EnergyStorageDailyClean实体转换为VO
     */
    private DailyTradeInfoVO convertEnergyStorageDailyCleanToVO(EnergyStorageDailyClean data) {
        DailyTradeInfoVO vo = new DailyTradeInfoVO();

        // 基础信息
        if (data.getDate() != null) {
            vo.setDate(data.getDate().toString());
        }
        vo.setStationId(data.getStationId());
        vo.setStationType("storage");

        // 储能用户侧字段映射
        vo.setUserDayAheadDeviationPower(data.getUserDayAheadDeviationPower());
        vo.setUserDayAheadDeviationAveragePrice(data.getUserDayAheadDeviationAveragePrice());
        vo.setUserDayAheadDeviationFee(data.getUserDayAheadDeviationFee());
        vo.setUserRealtimeDeviationPower(data.getUserRealTimeDeviationPower());
        vo.setUserRealtimeDeviationAveragePrice(data.getUserRealTimeDeviationAveragePrice());
        vo.setUserRealtimeDeviationFee(data.getUserRealTimeDeviationFee());
        vo.setUserTotalPower(data.getUserTotalPower());
        vo.setUserTotalFee(data.getUserTotalFee());

        // 储能发电侧字段映射
        vo.setPowerGenerationDayAheadDeviationPower(data.getPowerGenerationDayAheadDeviationPower());
        vo.setPowerGenerationDayAheadDeviationAveragePrice(data.getPowerGenerationDayAheadDeviationAveragePrice());
        vo.setPowerGenerationDayAheadDeviationFee(data.getPowerGenerationDayAheadDeviationFee());
        vo.setPowerGenerationRealtimeDeviationPower(data.getPowerGenerationRealTimeDeviationPower());
        vo.setPowerGenerationRealtimeDeviationAveragePrice(data.getPowerGenerationRealTimeDeviationAveragePrice());
        vo.setPowerGenerationRealtimeDeviationFee(data.getPowerGenerationRealTimeDeviationFee());
        vo.setPowerGenerationTotalPower(data.getPowerGenerationTotalPower());
        vo.setPowerGenerationTotalFee(data.getPowerGenerationTotalFee());

        return vo;
    }
}
