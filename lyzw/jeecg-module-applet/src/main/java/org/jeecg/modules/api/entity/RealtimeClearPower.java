package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("realtime_clear_power")
@ApiModel(value = "RealtimeClearPower", description = "实时日内各时段出清电量表")
public class RealtimeClearPower {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "日期", example = "2023-10-01")
    private LocalDate date;

    @ApiModelProperty(value = "时间（格式 HH:mm）", example = "09:00")
    private String time;

    @ApiModelProperty(value = "出清电量（单位: MWh）", example = "100.50")
    private BigDecimal value;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01 08:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-10-01 09:30:00")
    private LocalDateTime updateTime;
}