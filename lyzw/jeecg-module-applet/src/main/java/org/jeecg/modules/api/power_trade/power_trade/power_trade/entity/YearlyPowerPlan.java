package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 年度发电计划
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Data
@TableName("yearly_power_plan")
@ApiModel(value = "yearly_power_plan对象", description = "年度发电计划")
public class YearlyPowerPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    /**
     * 关联电站id
     */
    @TableField("station_id")
    @ApiModelProperty(value = "关联电站id", example = "1001")
    private Long stationId;

    /**
     * 年份
     */
    @TableField("year")
    @ApiModelProperty(value = "年份", example = "2024")
    private String year;

    /**
     * 月份
     */
    @TableField("month")
    @ApiModelProperty(value = "月份", example = "01")
    private String month;

    /**
     * 计划发电值
     */
    @TableField("plan_value")
    @ApiModelProperty(value = "计划发电值", example = "1000.500000")
    private BigDecimal planValue;

    /**
     * 实际发电值
     */
    @TableField("actual_value")
    @ApiModelProperty(value = "实际发电值", example = "950.300000")
    private BigDecimal actualValue;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;
}