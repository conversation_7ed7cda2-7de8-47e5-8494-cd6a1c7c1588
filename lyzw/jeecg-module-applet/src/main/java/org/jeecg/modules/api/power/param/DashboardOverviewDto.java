package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 首页总览数据DTO
 */
@Data
public class DashboardOverviewDto {
    
    /**
     * 交易容量(MW/GW)
     */
    private String tradingCapacity;
    
    /**
     * 场站数量
     */
    private Integer stationCount;
    
    /**
     * 风电场站数量
     */
    private Integer windStationCount;
    
    /**
     * 光伏场站数量
     */
    private Integer solarStationCount;
    
    /**
     * 储能场站数量
     */
    private Integer storageStationCount;
    
    /**
     * 交易类型数量（按区域）
     */
    private Integer tradeTypeCount;
    
    /**
     * 累计发电量(GWh)
     */
    private Double totalPowerGeneration;
    
    /**
     * 计划发电量(GWh)
     */
    private Double plannedPowerGeneration;
    
    /**
     * 限电量
     */
    private Double limitedPowerGeneration;
    
    /**
     * 结算电量
     */
    private Double settledPowerGeneration;
    
    /**
     * 结算均价(元/MWh)
     */
    private Double averageSettlementPrice;
    
    /**
     * 燃煤标杆价(元/MWh)
     */
    private Double coalBenchmarkPrice;
} 