package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;

import java.util.List;

public interface EnergyStorageDailyCleanService extends IService<EnergyStorageDailyClean> {
    
    /**
     * 按日查询明细数据
     */
    List<EnergyStorageDailyClean> selectByDay(String stationId, String date);
    
    /**
     * 按月汇总查询（支持数据补全）
     */
    List<EnergyStorageDailyClean> selectByMonth(Long stationId, String date);
    
    /**
     * 按年汇总查询
     */
    List<EnergyStorageDailyClean> selectByYear(String stationId, String date);
}