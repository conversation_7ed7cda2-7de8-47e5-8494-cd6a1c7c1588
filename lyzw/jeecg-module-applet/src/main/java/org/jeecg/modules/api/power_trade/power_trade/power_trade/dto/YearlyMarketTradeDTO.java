package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "YearlyMarketTradeDTO", description = "年度市场交易统计")
public class YearlyMarketTradeDTO {

    @ApiModelProperty(value = "数据类型", example = "现货交易")
    private String type;

    @ApiModelProperty(value = "年份", example = "2024")
    private String year;

    @ApiModelProperty(value = "年度交易总电量", example = "12000.50")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "年度交易均价", example = "0.45")
    private BigDecimal avgPrice;

    @ApiModelProperty(value = "年度放电均价", example = "0.40")
    private BigDecimal avgDischargePrice;

    @ApiModelProperty(value = "交易月份数", example = "12")
    private Integer monthCount;
}