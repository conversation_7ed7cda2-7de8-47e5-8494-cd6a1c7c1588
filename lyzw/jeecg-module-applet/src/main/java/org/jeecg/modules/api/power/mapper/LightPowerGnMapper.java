package org.jeecg.modules.api.power.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.power.entity.LightPowerGn;

import java.util.List;

@Mapper
public interface LightPowerGnMapper extends BaseMapper<LightPowerGn> {

    @Select({
            "<script>",
            "SELECT t.* ",
            "FROM forecast_power_report.light_power_gn t ",
            "JOIN (",
            "    SELECT `date`, MAX(version) AS max_version ",
            "    FROM forecast_power_report.light_power_gn ",
            "    WHERE station_id IN ",
            "    <foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "        #{sid}",
            "    </foreach>",
            "    AND `date` IN ",
            "    <foreach item='date' collection='dates' open='(' separator=',' close=')'>",
            "        #{date}",
            "    </foreach>",
            "    GROUP BY `date`",
            ") mv ON t.date = mv.date AND t.version = mv.max_version ",
            "WHERE t.station_id IN ",
            "<foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "    #{sid}",
            "</foreach>",
            "ORDER BY t.date ASC, t.time ASC",
            "</script>"
    })
    List<LightPowerGn> selectMaxVersionByDatesAndStation(
            @Param("dates") List<String> dates,  // 日期列表（如 ["2025-01-01", "2025-01-02"]）
            @Param("stationIds") List<Long> stationIds
    );

    @Select({
            "<script>",
            "SELECT t.* ",
            "FROM forecast_power_report.light_power_gn t ",
            "JOIN (",
            "    SELECT `date`, MAX(version) AS max_version ",
            "    FROM forecast_power_report.light_power_gn ",
            "    WHERE station_id IN ",
            "    <foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "        #{sid}",
            "    </foreach>",
            "    AND `date` BETWEEN #{startDate} AND #{endDate}",
            "    GROUP BY `date`",
            ") mv ON t.date = mv.date AND t.version = mv.max_version ",
            "WHERE t.station_id IN ",
            "<foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "    #{sid}",
            "</foreach>",
            "ORDER BY t.date ASC, t.time ASC",
            "</script>"
    })
    List<LightPowerGn> selectMaxVersionByDateRangeAndStation(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("stationIds") List<Long> stationIds
    );

    @Select({
            "<script>",
            "SELECT * ",
            "FROM forecast_power_report.light_power_gn ",
            "WHERE station_id IN ",
            "<foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "   #{sid}",
            "</foreach>",
            "AND `date` IN ",
            "<foreach item='date' collection='dates' open='(' separator=',' close=')'>",
            "   #{date}",
            "</foreach>",
            "AND version = #{version} ",  // 直接指定固定版本
            "ORDER BY `date` ASC, `time` ASC",
            "</script>"
    })
    List<LightPowerGn> selectByVersionAndDates(
            @Param("dates") List<String> dates,
            @Param("stationIds") List<Long> stationIds,
            @Param("version") String version
    );

    @Select({
            "<script>",
            "SELECT * ",
            "FROM forecast_power_report.light_power_gn ",
            "WHERE station_id IN ",
            "<foreach item='sid' collection='stationIds' open='(' separator=',' close=')'>",
            "   #{sid}",
            "</foreach>",
            "AND `date` BETWEEN #{startDate} AND #{endDate} ",
            "AND version = #{version} ",  // 直接指定固定版本
            "ORDER BY `date` ASC, `time` ASC",
            "</script>"
    })
    List<LightPowerGn> selectByVersionAndDateRange(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("stationIds") List<Long> stationIds,
            @Param("version") String version
    );

    @Select({
            "<script>",
            "SELECT MAX(version) AS version ",  // 直接查询最大版本
            "FROM forecast_power_report.light_power_gn ",
            "WHERE station_id = #{stationId} ",
            "AND `date` = #{date}",
            "</script>"
    })
    String selectMaxVersionByDate(
            @Param("date") String date,
            @Param("stationId") Long stationId
    );
}
