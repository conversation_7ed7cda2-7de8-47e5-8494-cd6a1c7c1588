package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.dto.StationDetailDTO;

import java.util.List;
import java.util.Map;

public interface YearlyPowerPlanMapper extends BaseMapper<YearlyPowerPlan> {

    /**
     * 获取电站详情（包含基础信息和年度发电统计）
     * @param stationId 电站ID
     * @param year 年份
     * @return 电站详情
     */
    StationDetailDTO getStationDetailWithPowerPlan(
            @Param("stationId") Long stationId,
            @Param("year") String year);

    /**
     * 获取电站年度发电计划统计
     * @param stationId 电站ID
     * @param year 年份
     * @return 统计数据
     */
    Map<String, Object> getStationYearlyPowerStats(
            @Param("stationId") Integer stationId,
            @Param("year") String year);

    /**
     * 获取电站月度发电计划明细
     * @param stationId 电站ID
     * @param year 年份
     * @return 月度明细列表
     */
    List<YearlyPowerPlan> getStationMonthlyPowerPlan(
            @Param("stationId") Long stationId,
            @Param("year") String year);
}