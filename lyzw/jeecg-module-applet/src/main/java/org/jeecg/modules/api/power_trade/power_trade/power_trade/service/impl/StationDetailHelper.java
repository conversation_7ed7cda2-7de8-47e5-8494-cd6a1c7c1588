package org.jeecg.modules.api.power_trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power.mapper.JiaYueRpMapper;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power.entity.RpJiaYue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 电站详情构建辅助类
 * 
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
@Slf4j
@Component
public class StationDetailHelper {

    @Autowired
    private JiaYueRpMapper jiaYueRpMapper;

    @Autowired
    private PowerSideSettleMapper powerSideSettleMapper;



    /**
     * 计算累计发电量 - 基于实际功率数据
     */
    public BigDecimal calculateTotalPowerGeneration(Long stationId, String year) {
        try {
            log.info("开始计算电站{}年度累计发电量 - 年份: {}", stationId, year);

            // 构建年度时间范围
            String startDate = year + "-01-01";
            String endDate = year + "-12-31";

            // 查询年度实际功率数据
            List<String> stationNos = Arrays.asList(String.valueOf(stationId));
            List<RpJiaYue> powerData = jiaYueRpMapper.selectMaxVersionByDateRangeAndStation(
                    startDate, endDate, stationNos, 1); // 1表示瞬时值

            if (powerData == null || powerData.isEmpty()) {
                log.warn("未查询到电站{}的年度功率数据", stationId);
                return BigDecimal.ZERO;
            }

            // 计算总发电量：与PowerService.java保持一致，实际功率 ÷ 4 = 发电量
            BigDecimal totalGeneration = powerData.stream()
                    .filter(rp -> rp.getValue() != null && rp.getValue() > 0)
                    .map(rp -> BigDecimal.valueOf(rp.getValue() / 4.0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            log.info("电站{}年度累计发电量计算完成 - 数据点数: {}, 总发电量: {} MWh",
                    stationId, powerData.size(), totalGeneration);

            return totalGeneration;

        } catch (Exception e) {
            log.error("计算累计发电量失败 - 电站ID: {}", stationId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取结算数据 - 从结算表中获取实际数据
     */
    public Map<String, Object> getSettlementData(Object station, String year, String month) {
        Map<String, Object> settlementData = new HashMap<>();

        try {
            log.info("开始获取结算数据 - 年份: {}, 月份: {}", year, month);

            // 获取电站ID
            Long stationId = null;
            if (station instanceof Map) {
                stationId = ((Map<String, Object>) station).get("id") instanceof Long ?
                        (Long) ((Map<String, Object>) station).get("id") : null;
            } else {
                // 尝试通过反射获取ID
                try {
                    stationId = (Long) station.getClass().getMethod("getId").invoke(station);
                } catch (Exception e) {
                    log.warn("无法从station对象获取ID", e);
                }
            }

            if (stationId == null) {
                log.warn("无法获取电站ID，返回默认结算数据");
                settlementData.put("current_month_power", BigDecimal.ZERO);
                settlementData.put("current_month_plan_power", BigDecimal.ZERO);
                settlementData.put("settlement_average_price", BigDecimal.ZERO);
                return settlementData;
            }

            // 构建月份查询参数
            String yearMonth = year + "-" + month;

            // 查询月度结算数据
            Map<String, Object> monthlyData = powerSideSettleMapper.getStationMonthlyTradingInfo(stationId, yearMonth);

            if (monthlyData != null && !monthlyData.isEmpty()) {
                // 从结算数据中提取所需字段
                BigDecimal currentMonthPower = (BigDecimal) monthlyData.getOrDefault("monthlyElectricity", BigDecimal.ZERO);
                BigDecimal currentMonthPlanPower = (BigDecimal) monthlyData.getOrDefault("monthlyPlanElectricity", BigDecimal.ZERO);
                BigDecimal settlementAvgPrice = (BigDecimal) monthlyData.getOrDefault("monthlyAveragePrice", BigDecimal.ZERO);

                settlementData.put("current_month_power", currentMonthPower);
                settlementData.put("current_month_plan_power", currentMonthPlanPower);
                settlementData.put("settlement_average_price", settlementAvgPrice);

                log.info("结算数据获取成功 - 电站ID: {}, 月度电量: {}, 计划电量: {}, 结算均价: {}",
                        stationId, currentMonthPower, currentMonthPlanPower, settlementAvgPrice);
            } else {
                log.warn("未查询到电站{}的月度结算数据 - 年月: {}", stationId, yearMonth);
                settlementData.put("current_month_power", BigDecimal.ZERO);
                settlementData.put("current_month_plan_power", BigDecimal.ZERO);
                settlementData.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.error("获取结算数据失败", e);
            settlementData.put("current_month_power", BigDecimal.ZERO);
            settlementData.put("current_month_plan_power", BigDecimal.ZERO);
            settlementData.put("settlement_average_price", BigDecimal.ZERO);
        }

        return settlementData;
    }

    /**
     * 获取省份名称
     */
    public String getProvinceName(Integer provinceId) {
        if (provinceId == null) return "未知省份";
        switch (provinceId) {
            case 1: return "安徽省";
            case 2: return "江苏省";
            default: return "未知省份";
        }
    }

    /**
     * 构建默认电站详情响应 - 简化版本，只包含基础信息和发电量
     */
    public StationDetailResponseDTO buildDefaultStationDetailResponse(Long stationId, String date, String dimension) {
        StationDetailResponseDTO response = new StationDetailResponseDTO();
        response.setBasicInfo(buildDefaultBasicInfo(stationId));
        response.setPowerGeneration(buildDefaultPowerGenerationInfo(dimension, date));
        return response;
    }

    /**
     * 构建默认基础信息
     */
    public StationDetailResponseDTO.BasicInfo buildDefaultBasicInfo(Long stationId) {
        StationDetailResponseDTO.BasicInfo basicInfo = new StationDetailResponseDTO.BasicInfo();
        basicInfo.setStationType("未知");
        basicInfo.setStationName("未知电站");
        basicInfo.setCapacity(BigDecimal.ZERO);
        basicInfo.setProvinceId(0);
        basicInfo.setProvinceName("未知省份");
        basicInfo.setYearPlanPowerGeneration(BigDecimal.ZERO);
        basicInfo.setTotalPowerGeneration(BigDecimal.ZERO);
        basicInfo.setCurrentMonthPower(BigDecimal.ZERO);
        basicInfo.setCurrentMonthPlanPower(BigDecimal.ZERO);
        basicInfo.setSettlementAveragePrice(BigDecimal.ZERO);
        return basicInfo;
    }

    /**
     * 构建默认发电量信息
     */
    public StationDetailResponseDTO.PowerGenerationInfo buildDefaultPowerGenerationInfo(String dimension, String date) {
        StationDetailResponseDTO.PowerGenerationInfo powerGeneration = new StationDetailResponseDTO.PowerGenerationInfo();
        powerGeneration.setCurrentPeriodGeneration(BigDecimal.ZERO);
        powerGeneration.setDetailData(new ArrayList<>());
        powerGeneration.setDimension(dimension);
        powerGeneration.setQueryDate(date);
        return powerGeneration;
    }

}
