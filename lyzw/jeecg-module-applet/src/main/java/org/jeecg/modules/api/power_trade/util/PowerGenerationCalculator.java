package org.jeecg.modules.api.power_trade.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.RpJiaYue;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发电量计算工具类
 * 
 * 统一发电量计算逻辑，与PowerService.java保持一致
 * 核心原理：实际功率(MW) ÷ 4 = 发电量(MWh)
 * 原因：数据是15分钟间隔，1小时=4个15分钟，所以功率值除以4得到每15分钟的发电量
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Slf4j
@Component
public class PowerGenerationCalculator {

    /**
     * 计算单个功率值对应的发电量
     * 
     * @param actualPower 实际功率(MW)
     * @return 发电量(MWh)
     */
    public static Double calculateGeneration(Double actualPower) {
        if (actualPower == null || actualPower <= 0) {
            return 0.0;
        }
        // 实际功率 ÷ 4 = 发电量（15分钟间隔）
        return actualPower / 4.0;
    }

    /**
     * 计算单个功率值对应的发电量（BigDecimal版本）
     * 
     * @param actualPower 实际功率(MW)
     * @return 发电量(MWh)
     */
    public static BigDecimal calculateGeneration(BigDecimal actualPower) {
        if (actualPower == null || actualPower.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        // 实际功率 ÷ 4 = 发电量（15分钟间隔）
        return actualPower.divide(BigDecimal.valueOf(4.0), 6, RoundingMode.HALF_UP);
    }

    /**
     * 计算功率数据列表的总发电量
     * 
     * @param powerDataList 功率数据列表
     * @return 总发电量(MWh)
     */
    public static BigDecimal calculateTotalGeneration(List<RpJiaYue> powerDataList) {
        if (powerDataList == null || powerDataList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return powerDataList.stream()
                .filter(rp -> rp.getValue() != null && rp.getValue() > 0)
                .map(rp -> calculateGeneration(BigDecimal.valueOf(rp.getValue())))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 按日期分组计算每日发电量
     * 
     * @param powerDataList 功率数据列表
     * @return 日期 -> 发电量的映射
     */
    public static Map<String, BigDecimal> calculateDailyGeneration(List<RpJiaYue> powerDataList) {
        if (powerDataList == null || powerDataList.isEmpty()) {
            return Map.of();
        }

        return powerDataList.stream()
                .filter(rp -> rp.getValue() != null && rp.getValue() > 0 && rp.getDate() != null)
                .collect(Collectors.groupingBy(
                        RpJiaYue::getDate,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                rp -> calculateGeneration(BigDecimal.valueOf(rp.getValue())),
                                BigDecimal::add
                        )
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().setScale(2, RoundingMode.HALF_UP)
                ));
    }

    /**
     * 按月份分组计算每月发电量
     * 
     * @param powerDataList 功率数据列表
     * @return 月份 -> 发电量的映射
     */
    public static Map<String, BigDecimal> calculateMonthlyGeneration(List<RpJiaYue> powerDataList) {
        if (powerDataList == null || powerDataList.isEmpty()) {
            return Map.of();
        }

        return powerDataList.stream()
                .filter(rp -> rp.getValue() != null && rp.getValue() > 0 && rp.getDate() != null)
                .collect(Collectors.groupingBy(
                        rp -> rp.getDate().substring(0, 7), // 提取年-月部分
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                rp -> calculateGeneration(BigDecimal.valueOf(rp.getValue())),
                                BigDecimal::add
                        )
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().setScale(2, RoundingMode.HALF_UP)
                ));
    }

    /**
     * 验证发电量计算的正确性
     * 
     * @param actualPower 实际功率(MW)
     * @param expectedGeneration 期望的发电量(MWh)
     * @return 是否匹配
     */
    public static boolean validateGeneration(Double actualPower, Double expectedGeneration) {
        if (actualPower == null || expectedGeneration == null) {
            return false;
        }
        
        Double calculatedGeneration = calculateGeneration(actualPower);
        // 允许小的浮点数误差
        return Math.abs(calculatedGeneration - expectedGeneration) < 0.0001;
    }

    /**
     * 反推实际功率（用于显示）
     * 
     * @param generation 发电量(MWh)
     * @return 实际功率(MW)
     */
    public static Double calculateActualPowerFromGeneration(Double generation) {
        if (generation == null || generation <= 0) {
            return 0.0;
        }
        // 发电量 × 4 = 实际功率
        return generation * 4.0;
    }

    /**
     * 反推实际功率（BigDecimal版本）
     * 
     * @param generation 发电量(MWh)
     * @return 实际功率(MW)
     */
    public static BigDecimal calculateActualPowerFromGeneration(BigDecimal generation) {
        if (generation == null || generation.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        // 发电量 × 4 = 实际功率
        return generation.multiply(BigDecimal.valueOf(4.0)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化发电量显示
     * 
     * @param generation 发电量
     * @param unit 单位（如：MWh, GWh）
     * @return 格式化后的字符串
     */
    public static String formatGeneration(BigDecimal generation, String unit) {
        if (generation == null) {
            return "0.00 " + unit;
        }
        return generation.setScale(2, RoundingMode.HALF_UP).toString() + " " + unit;
    }

    /**
     * 计算发电量增长率
     * 
     * @param currentGeneration 当前发电量
     * @param previousGeneration 之前发电量
     * @return 增长率（百分比）
     */
    public static BigDecimal calculateGrowthRate(BigDecimal currentGeneration, BigDecimal previousGeneration) {
        if (previousGeneration == null || previousGeneration.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        if (currentGeneration == null) {
            return BigDecimal.valueOf(-100); // -100%
        }
        
        return currentGeneration.subtract(previousGeneration)
                .divide(previousGeneration, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 记录计算过程日志
     * 
     * @param stationId 电站ID
     * @param dataCount 数据点数
     * @param totalGeneration 总发电量
     * @param timeRange 时间范围
     */
    public static void logCalculationResult(Long stationId, int dataCount, BigDecimal totalGeneration, String timeRange) {
        log.info("发电量计算完成 - 电站ID: {}, 时间范围: {}, 数据点数: {}, 总发电量: {} MWh", 
                stationId, timeRange, dataCount, totalGeneration);
    }
}
