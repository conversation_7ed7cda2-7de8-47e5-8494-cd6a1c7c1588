package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 电站日交易信息VO
 */
@Data
@ApiModel(value = "StationDailyTradeVO", description = "电站日交易信息")
public class StationDailyTradeVO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "交易日期")
    private LocalDate tradeDate;

    @ApiModelProperty(value = "区域代码")
    private Integer provinceId;

    @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)")
    private Integer stationType;

    @ApiModelProperty(value = "侧别类型 (user_side-用户侧, generation_side-发电侧) - 仅储能")
    private String sideType;

    // ========== 安徽光伏风电字段 ==========
    @ApiModelProperty(value = "中长期合约电量(MWh)")
    private BigDecimal longTermContractPower;

    @ApiModelProperty(value = "中长期单价(元/MWh)")
    private BigDecimal longTermUnitPrice;

    @ApiModelProperty(value = "中长期电费(元)")
    private BigDecimal longTermFee;

    @ApiModelProperty(value = "保障电量(MWh)")
    private BigDecimal guaranteedPower;

    @ApiModelProperty(value = "保障电价(元/MWh)")
    private BigDecimal guaranteedPrice;

    @ApiModelProperty(value = "保障电费(元)")
    private BigDecimal guaranteedFee;

    @ApiModelProperty(value = "日前偏差电量(MWh)")
    private BigDecimal dayAheadDeviationPower;

    @ApiModelProperty(value = "日前偏差单价(元/MWh)")
    private BigDecimal dayAheadDeviationPrice;

    @ApiModelProperty(value = "日前偏差电费(元)")
    private BigDecimal dayAheadDeviationFee;

    @ApiModelProperty(value = "实时偏差电量(MWh)")
    private BigDecimal realTimeDeviationPower;

    // ========== 安徽储能字段 ==========
    @ApiModelProperty(value = "日前平均电价(元/MWh)")
    private BigDecimal dayAheadAvgPrice;

    @ApiModelProperty(value = "日前电费(元)")
    private BigDecimal dayAheadFee;

    @ApiModelProperty(value = "实时平均单价(元/MWh)")
    private BigDecimal realTimeAvgPrice;

    @ApiModelProperty(value = "实时电费(元)")
    private BigDecimal realTimeFee;

    @ApiModelProperty(value = "总电量(MWh)")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "总费用(元)")
    private BigDecimal totalFee;

    // ========== 江苏光伏风电字段 ==========
    @ApiModelProperty(value = "上网电量(MWh)")
    private BigDecimal gridPower;

    @ApiModelProperty(value = "保障性电量(MWh)")
    private BigDecimal guaranteedPowerJs;

    @ApiModelProperty(value = "保障性电费(元)")
    private BigDecimal guaranteedFeeJs;

    @ApiModelProperty(value = "合约叠加分区价差电量(MWh)")
    private BigDecimal contractZoneDiffPower;

    @ApiModelProperty(value = "合约叠加分区价差电费(元)")
    private BigDecimal contractZoneDiffFee;

    @ApiModelProperty(value = "实时偏差电量(MWh)")
    private BigDecimal realTimeDeviationPowerJs;

    @ApiModelProperty(value = "实时偏差电费(元)")
    private BigDecimal realTimeDeviationFeeJs;

    @ApiModelProperty(value = "k值返还回收及不平衡费用(元)")
    private BigDecimal kValueFee;

    @ApiModelProperty(value = "成本补偿与分摊类费用(元)")
    private BigDecimal costCompensationFee;

    @ApiModelProperty(value = "交易执行考核和返还费用(元)")
    private BigDecimal tradeExecutionFee;

    @ApiModelProperty(value = "总结算电量(MWh)")
    private BigDecimal totalSettlementPower;

    @ApiModelProperty(value = "总结算电费(元)")
    private BigDecimal totalSettlementFee;

    @ApiModelProperty(value = "结算均价(元/MWh)")
    private BigDecimal settlementAvgPrice;

    // ========== 开发状态标识 ==========
    @ApiModelProperty(value = "是否为开发中状态")
    private Boolean isDevelopment;

    @ApiModelProperty(value = "开发状态消息")
    private String developmentMessage;

    /**
     * 获取侧别类型显示名称
     */
    public String getSideTypeName() {
        if (sideType == null) {
            return "";
        }
        switch (sideType) {
            case "user_side":
                return "用户侧";
            case "generation_side":
                return "发电侧";
            default:
                return sideType;
        }
    }

    /**
     * 判断是否为安徽光伏风电
     */
    public boolean isAnhuiRenewable() {
        return "ah".equals(provinceId) && (stationType == 1 || stationType == 2);
    }

    /**
     * 判断是否为安徽储能
     */
    public boolean isAnhuiStorage() {
        return "ah".equals(provinceId) && stationType == 3;
    }

    /**
     * 判断是否为江苏光伏风电
     */
    public boolean isJiangsuRenewable() {
        return "js".equals(provinceId) && (stationType == 1 || stationType == 2);
    }

    /**
     * 判断是否为江苏储能
     */
    public boolean isJiangsuStorage() {
        return "js".equals(provinceId) && stationType == 3;
    }

    /**
     * 设置为开发中状态
     */
    public void setDevelopmentStatus(String message) {
        this.isDevelopment = true;
        this.developmentMessage = message;
    }

    /**
     * 获取电站类型名称
     */
    public String getStationTypeName() {
        if (stationType == null) {
            return "未知";
        }
        switch (stationType) {
            case 1:
                return "风电";
            case 2:
                return "光伏";
            case 3:
                return "储能";
            default:
                return "未知";
        }
    }

    /**
     * 获取交易日期字符串
     */
    public String getTradeDateString() {
        return tradeDate != null ? tradeDate.toString() : "";
    }
}
