package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.FileStationRelation;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;
import org.jeecg.modules.api.power_trade.mapper.FileStationRelationMapper;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power_trade.service.PowerSideSettleService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PowerSideSettleServiceImpl extends ServiceImpl<PowerSideSettleMapper, PowerSideSettle> implements PowerSideSettleService {

    private final FileStationRelationMapper fileStationRelationMapper;

    public PowerSideSettleServiceImpl(FileStationRelationMapper fileStationRelationMapper) {
        this.fileStationRelationMapper = fileStationRelationMapper;
    }

    @Override
    public Map<String, BigDecimal> getStationSettlementSummary(Long stationId, String startDate, String endDate,
                                                               String startYearMonth, String endYearMonth) {
        log.info("开始查询电站结算汇总数据 - 电站ID: {}, 开始日期: {}, 结束日期: {}, 开始年月: {}, 结束年月: {}",
                stationId, startDate, endDate, startYearMonth, endYearMonth);

        Map<String, BigDecimal> result = baseMapper.getStationSettlementSummary(stationId, startDate, endDate, startYearMonth, endYearMonth);

        if (result != null && !result.isEmpty()) {
            log.info("电站结算数据查询成功 - 电站ID: {}, 累计结算电量: {}, 累计结算电费: {}, 交易均价: {}",
                    stationId,
                    result.get("totalSettlementElectricity"),
                    result.get("totalSettlementElectricFee"),
                    result.get("avgTradePrice"));
        } else {
            log.warn("电站结算数据查询结果为空 - 电站ID: {}", stationId);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getProvinceStationSettlementSummary(Integer provinceId, String startDate, String endDate,
                                                                         String startYearMonth, String endYearMonth) {
        return baseMapper.getProvinceStationSettlementSummary(provinceId, startDate, endDate, startYearMonth, endYearMonth);
    }

    @Override
    public Map<String, Object> getStationYearlyTradingInfo(Long stationId, String year) {
        log.info("开始查询电站年度交易信息 - 电站ID: {}, 年份: {}", stationId, year);

        // 验证年份格式
        if (!year.matches("\\d{4}")) {
            throw new IllegalArgumentException("年份格式不正确，请使用yyyy格式");
        }
        try {
            // 构建年度时间范围
            LocalDate firstDayOfYear = Year.of(Integer.parseInt(year)).atDay(1);
            LocalDate lastDayOfYear = Year.of(Integer.parseInt(year)).atMonth(12).atEndOfMonth();

            // 使用lambda表达式获取年度文件关系数据
            List<FileStationRelation> yearlyRelations = Optional.ofNullable(
                    fileStationRelationMapper.selectList(
                            new LambdaQueryWrapper<FileStationRelation>()
                                    .eq(FileStationRelation::getStationId, stationId)
                                    .ge(FileStationRelation::getSettleDate, firstDayOfYear)
                                    .le(FileStationRelation::getSettleDate, lastDayOfYear)
                    )
            ).orElse(Collections.emptyList());

            if (yearlyRelations.isEmpty()) {
                log.warn("未查询到电站{}年度{}的交易数据", stationId, year);
                return buildEmptyYearlyResult(stationId, year);
            }

            // 先按月份分组file_id，这样逻辑更清晰
            Map<String, List<Long>> fileIdsByMonth = yearlyRelations.stream()
                    .filter(relation -> relation.getSettleDate() != null && relation.getId() != null)
                    .collect(Collectors.groupingBy(
                            relation -> relation.getSettleDate().getYear() + "-" +
                                       String.format("%02d", relation.getSettleDate().getMonthValue()),
                            LinkedHashMap::new,  // 保持月份顺序
                            Collectors.mapping(FileStationRelation::getId, Collectors.toList())
                    ));

            // 基于月份分组的file_id获取结算数据，结果自然按月份分组
            Map<String, List<PowerSideSettle>> monthlySettleData = fileIdsByMonth.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,  // 月份作为key
                            entry -> entry.getValue().stream()  // file_id列表
                                    .distinct()
                                    .map(fileId -> Optional.ofNullable(
                                            baseMapper.selectList(
                                                    new LambdaQueryWrapper<PowerSideSettle>()
                                                            .eq(PowerSideSettle::getFileId, fileId)
                                            )
                                    ).orElse(Collections.emptyList()))
                                    .flatMap(List::stream)
                                    .collect(Collectors.toList())
                    ));

            // 构建年度交易信息结果
            return buildYearlyTradingResultOptimized(stationId, year, monthlySettleData);

        } catch (Exception e) {
            log.error("查询电站年度交易信息失败 - 电站ID: {}, 年份: {}", stationId, year, e);
            throw new RuntimeException("查询电站年度交易信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getStationMonthlyTradingInfo(Long stationId, String year) {
        log.info("开始查询电站月度交易信息 - 电站ID: {}, 年月: {}", stationId, year);

        // 验证年月格式
        if (!year.matches("\\d{4}")) {
            throw new IllegalArgumentException("年份格式不正确，请使用yyyy-MM格式");
        }

        try {
            // 解析年月，构建查询时间范围
            YearMonth ym = YearMonth.parse(year);
            LocalDate firstDayOfMonth = ym.atDay(1);
            LocalDate lastDayOfMonth = ym.atEndOfMonth();

            // 获取月度文件关系数据，按file_id分组
            Map<Long, List<FileStationRelation>> fileRelationsMap = Optional.ofNullable(
                    fileStationRelationMapper.selectList(
                            new LambdaQueryWrapper<FileStationRelation>()
                                    .eq(FileStationRelation::getStationId, stationId)
                                    .ge(FileStationRelation::getSettleDate, firstDayOfMonth)
                                    .le(FileStationRelation::getSettleDate, lastDayOfMonth)
                    )
            ).orElse(Collections.emptyList())
            .stream()
            .filter(relation -> relation.getId() != null)
            .collect(Collectors.groupingBy(FileStationRelation::getId));

            if (fileRelationsMap.isEmpty()) {
                log.warn("未查询到电站{}年{}的交易数据", stationId, year);
                return buildEmptyMonthlyResult(stationId, year);
            }

            // 对每个file_id，获取其对应的全部power_side_settle数据
            Map<Long, List<PowerSideSettle>> fileSettleDataMap = fileRelationsMap.keySet().stream()
                    .collect(Collectors.toMap(
                            fileId -> fileId,
                            fileId -> Optional.ofNullable(
                                    baseMapper.selectList(
                                            new LambdaQueryWrapper<PowerSideSettle>()
                                                    .eq(PowerSideSettle::getFileId, fileId)
                                    )
                            ).orElse(Collections.emptyList())
                    ));

            // 构建返回结果，对所有字段进行求和
            return buildMonthlyTradingResultWithAllFields(stationId, year, fileSettleDataMap);

        } catch (Exception e) {
            log.error("查询电站月度交易信息失败 - 电站ID: {}, 年月: {}", stationId, year, e);
            throw new RuntimeException("查询电站月度交易信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getStationMonthlyTradingInfoByYear(Long stationId, String year, Integer stationType) {

        // 验证年份格式
        if (!year.matches("\\d{4}")) {
            throw new IllegalArgumentException("年份格式不正确，请使用yyyy格式");
        }

        // 验证电站类型
        if (stationType == null || (stationType != 1 && stationType != 2 && stationType != 3)) {
            throw new IllegalArgumentException("电站类型参数错误，支持的类型：1-光伏，2-风电，3-储能");
        }

        try {
            // 根据电站类型调用不同的查询方法
            Map<String, Object> result;
            if (stationType == 3) {
                // 储能电站查询
                result = baseMapper.getStorageStationMonthlyTradingInfoByYear(stationId, year);
            } else {
                // 光伏/风电电站查询
                result = baseMapper.getNewEnergyStationMonthlyTradingInfoByYear(stationId, year);
            }

            if (result == null) {
                result = new HashMap<>();
                result.put("stationId", stationId);
                result.put("year", year);
                result.put("stationType", stationType);
                result.put("monthlyData", new ArrayList<>());
                log.warn("未查询到电站月度交易数据 - 电站ID: {}, 年份: {}, 电站类型: {}", stationId, year, stationType);
            }

            log.info("电站月度交易信息查询完成 - 电站ID: {}, 返回数据: {}", stationId, result);
            return result;

        } catch (Exception e) {
            log.error("查询电站月度交易信息失败 - 电站ID: {}, 年份: {}, 电站类型: {}", stationId, year, stationType, e);
            throw new RuntimeException("查询电站月度交易信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建空的年度结果
     */
    private Map<String, Object> buildEmptyYearlyResult(Long stationId, String year) {
        Map<String, Object> result = new HashMap<>();
        result.put("stationId", stationId);
        result.put("year", year);
        result.put("monthlyData", Collections.emptyList());
        result.put("totalElectricity", BigDecimal.ZERO);
        result.put("totalFee", BigDecimal.ZERO);
        result.put("averagePrice", BigDecimal.ZERO);
        return result;
    }

    /**
     * 构建空的月度结果
     */
    private Map<String, Object> buildEmptyMonthlyResult(Long stationId, String yearMonth) {
        Map<String, Object> result = new HashMap<>();
        result.put("stationId", stationId);
        result.put("yearMonth", yearMonth);
        result.put("monthlyElectricity", BigDecimal.ZERO);
        result.put("monthlyElectricFee", BigDecimal.ZERO);
        result.put("monthlyAveragePrice", BigDecimal.ZERO);
        result.put("recordCount", 0);
        return result;
    }

    /**
     * 构建年度交易信息结果（优化版本）
     */
    private Map<String, Object> buildYearlyTradingResultOptimized(Long stationId, String year,
                                                                 Map<String, List<PowerSideSettle>> monthlySettleData) {

        Map<String, Object> result = new HashMap<>();

        // 修改1：实现monthlyData的12个月补零功能
        List<Map<String, Object>> monthlyDataList = buildComplete12MonthsData(year, monthlySettleData);

        // 修改2：新增6个字段的月度分组数据
        List<Map<String, Object>> monthlyDetailedData = buildMonthlyDetailedData(year, monthlySettleData);

        // 获取所有年度结算数据，直接对原始数据进行求和
        List<PowerSideSettle> allYearlySettles = monthlySettleData.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 对所有字段进行求和
        BigDecimal totalActualInternetElectricity = allYearlySettles.stream()
                .filter(settle -> settle.getActualInternetElectricity() != null)
                .map(PowerSideSettle::getActualInternetElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalSettlementElectricity = allYearlySettles.stream()
                .filter(settle -> settle.getSettlementElectricity() != null)
                .map(PowerSideSettle::getSettlementElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalContractElectricity = allYearlySettles.stream()
                .filter(settle -> settle.getContractElectricity() != null)
                .map(PowerSideSettle::getContractElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalDeviationElectricity = allYearlySettles.stream()
                .filter(settle -> settle.getDeviationElectricity() != null)
                .map(PowerSideSettle::getDeviationElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalSettlementElectricFee = allYearlySettles.stream()
                .filter(settle -> settle.getSettlementElectricFee() != null)
                .map(PowerSideSettle::getSettlementElectricFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算平均电价：总结算电费 ÷ 总结算电量
        BigDecimal averagePrice = totalSettlementElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                totalSettlementElectricFee.divide(totalSettlementElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        // 构建返回结果
        result.put("stationId", stationId);
        result.put("year", year);
        result.put("monthlyData", monthlyDataList);                                    // 12个月补零的月度数据
        result.put("monthlyDetailedData", monthlyDetailedData);                        // 新增：6个字段的月度分组数据
        result.put("totalActualInternetElectricity", totalActualInternetElectricity);  // 实际上网电量总计
        result.put("totalSettlementElectricity", totalSettlementElectricity);          // 结算电量总计
        result.put("totalContractElectricity", totalContractElectricity);              // 合同电量总计
        result.put("totalDeviationElectricity", totalDeviationElectricity);            // 偏差电量总计
        result.put("totalSettlementElectricFee", totalSettlementElectricFee);          // 结算电费总计
        result.put("averagePrice", averagePrice);                                      // 平均电价
        result.put("monthCount", monthlyDataList.size());                             // 月份数量(固定为12)
        result.put("totalRecordCount", allYearlySettles.size());                      // 总记录数

        log.info("电站年度交易信息构建完成 - 电站ID: {}, 年份: {}, 月份数: {}, 详细数据月份数: {}, " +
                "实际上网电量: {} MWh, 结算电量: {} MWh, 合同电量: {} MWh, 偏差电量: {} MWh, " +
                "结算电费: {} 万元, 平均电价: {} 元/MWh, 总记录数: {}",
                stationId, year, monthlyDataList.size(), monthlyDetailedData.size(),
                totalActualInternetElectricity, totalSettlementElectricity, totalContractElectricity,
                totalDeviationElectricity, totalSettlementElectricFee, averagePrice, allYearlySettles.size());

        return result;
    }

    /**
     * 构建完整的12个月数据（补零功能）
     * 确保返回1-12月的完整数据，无数据月份填充零值
     */
    private List<Map<String, Object>> buildComplete12MonthsData(String year, Map<String, List<PowerSideSettle>> monthlySettleData) {
        List<Map<String, Object>> complete12MonthsData = new ArrayList<>();

        // 生成1-12月的完整月份列表
        for (int month = 1; month <= 12; month++) {
            String monthKey = String.format("%s-%02d", year, month);

            if (monthlySettleData.containsKey(monthKey)) {
                // 有数据的月份，计算实际数据
                List<PowerSideSettle> monthlySettles = monthlySettleData.get(monthKey);
                Map<String, Object> monthlyData = calculateMonthlyDataOptimized(monthKey, monthlySettles);
                complete12MonthsData.add(monthlyData);
            } else {
                // 无数据的月份，填充零值
                Map<String, Object> emptyMonthData = new HashMap<>();
                emptyMonthData.put("month", monthKey);
                emptyMonthData.put("electricity", BigDecimal.ZERO);  // 月度交易电量
                emptyMonthData.put("fee", BigDecimal.ZERO);          // 月度交易电费
                emptyMonthData.put("averagePrice", BigDecimal.ZERO); // 月度交易均价
                emptyMonthData.put("recordCount", 0);                // 记录数
                complete12MonthsData.add(emptyMonthData);
            }
        }

        log.debug("12个月完整数据构建完成 - 年份: {}, 总月份数: {}", year, complete12MonthsData.size());
        return complete12MonthsData;
    }

    /**
     * 构建6个字段的月度分组数据
     * 包含：actualInternetElectricity, settlementElectricity, contractElectricity,
     *      deviationElectricity, settlementElectricFee, averagePrice
     */
    private List<Map<String, Object>> buildMonthlyDetailedData(String year, Map<String, List<PowerSideSettle>> monthlySettleData) {
        List<Map<String, Object>> monthlyDetailedData = new ArrayList<>();

        // 只处理有数据的月份，不进行补零
        monthlySettleData.entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按月份排序
                .forEach(entry -> {
                    String month = entry.getKey();
                    List<PowerSideSettle> monthlySettles = entry.getValue();

                    // 计算月度6个字段的汇总数据
                    Map<String, Object> detailedData = calculateMonthlyDetailedData(month, monthlySettles);
                    monthlyDetailedData.add(detailedData);
                });

        log.debug("月度详细数据构建完成 - 年份: {}, 有数据月份数: {}", year, monthlyDetailedData.size());
        return monthlyDetailedData;
    }

    /**
     * 计算月度详细数据（6个字段）
     */
    private Map<String, Object> calculateMonthlyDetailedData(String month, List<PowerSideSettle> monthlySettles) {
        Map<String, Object> detailedData = new HashMap<>();

        // 实际上网电量
        BigDecimal actualInternetElectricity = monthlySettles.stream()
                .filter(settle -> settle.getActualInternetElectricity() != null)
                .map(PowerSideSettle::getActualInternetElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 结算电量
        BigDecimal settlementElectricity = monthlySettles.stream()
                .filter(settle -> settle.getSettlementElectricity() != null)
                .map(PowerSideSettle::getSettlementElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 合同电量
        BigDecimal contractElectricity = monthlySettles.stream()
                .filter(settle -> settle.getContractElectricity() != null)
                .map(PowerSideSettle::getContractElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 偏差电量
        BigDecimal deviationElectricity = monthlySettles.stream()
                .filter(settle -> settle.getDeviationElectricity() != null)
                .map(PowerSideSettle::getDeviationElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 结算电费
        BigDecimal settlementElectricFee = monthlySettles.stream()
                .filter(settle -> settle.getSettlementElectricFee() != null)
                .map(PowerSideSettle::getSettlementElectricFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 平均电价 = 结算电费 ÷ 结算电量
        BigDecimal averagePrice = settlementElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                settlementElectricFee.divide(settlementElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        detailedData.put("month", month);
        detailedData.put("actualInternetElectricity", actualInternetElectricity);
        detailedData.put("settlementElectricity", settlementElectricity);
        detailedData.put("contractElectricity", contractElectricity);
        detailedData.put("deviationElectricity", deviationElectricity);
        detailedData.put("settlementElectricFee", settlementElectricFee);
        detailedData.put("averagePrice", averagePrice);
        detailedData.put("recordCount", monthlySettles.size());

        log.debug("月度详细数据计算完成 - 月份: {}, 实际上网电量: {} MWh, 结算电量: {} MWh, " +
                "合同电量: {} MWh, 偏差电量: {} MWh, 结算电费: {} 万元, 平均电价: {} 元/MWh, 记录数: {}",
                month, actualInternetElectricity, settlementElectricity, contractElectricity,
                deviationElectricity, settlementElectricFee, averagePrice, monthlySettles.size());

        return detailedData;
    }

    /**
     * 计算月度数据（优化版本）- 直接基于已分组的结算数据
     * 月度交易电量 = settlement_electricity字段求和
     * 月度交易均价 = settlement_electric_fee字段求和 ÷ settlement_electricity字段求和
     */
    private Map<String, Object> calculateMonthlyDataOptimized(String month, List<PowerSideSettle> monthlySettles) {

        Map<String, Object> monthlyData = new HashMap<>();

        // 月度交易电量：settlement_electricity字段求和
        BigDecimal monthlyElectricity = monthlySettles.stream()
                .filter(settle -> settle.getSettlementElectricity() != null)
                .map(PowerSideSettle::getSettlementElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 月度交易电费：settlement_electric_fee字段求和
        BigDecimal monthlyFee = monthlySettles.stream()
                .filter(settle -> settle.getSettlementElectricFee() != null)
                .map(PowerSideSettle::getSettlementElectricFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 月度交易均价 = 月度交易电费 ÷ 月度交易电量
        BigDecimal monthlyAvgPrice = monthlyElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                monthlyFee.divide(monthlyElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        monthlyData.put("month", month);
        monthlyData.put("electricity", monthlyElectricity);  // 月度交易电量
        monthlyData.put("fee", monthlyFee);                  // 月度交易电费
        monthlyData.put("averagePrice", monthlyAvgPrice);    // 月度交易均价
        monthlyData.put("recordCount", monthlySettles.size());

        log.debug("月度数据计算完成 - 月份: {}, 交易电量: {} MWh, 交易电费: {} 万元, 交易均价: {} 元/MWh, 记录数: {}",
                month, monthlyElectricity, monthlyFee, monthlyAvgPrice, monthlySettles.size());

        return monthlyData;
    }

    /**
     * 计算月度数据（保留原版本以兼容）
     * 月度交易电量 = settlement_electricity字段求和
     * 月度交易均价 = settlement_electric_fee字段求和 ÷ settlement_electricity字段求和
     */
    private Map<String, Object> calculateMonthlyData(String month, List<FileStationRelation> monthlyRelations,
                                                    Map<Long, List<PowerSideSettle>> settleDataMap) {

        Map<String, Object> monthlyData = new HashMap<>();

        // 月度交易电量：settlement_electricity字段求和
        BigDecimal monthlyElectricity = monthlyRelations.stream()
                .map(FileStationRelation::getId)
                .filter(Objects::nonNull)
                .map(settleDataMap::get)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(settle -> settle.getSettlementElectricity() != null)
                .map(PowerSideSettle::getSettlementElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 月度交易电费：settlement_electric_fee字段求和
        BigDecimal monthlyFee = monthlyRelations.stream()
                .map(FileStationRelation::getId)
                .filter(Objects::nonNull)
                .map(settleDataMap::get)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(settle -> settle.getSettlementElectricFee() != null)
                .map(PowerSideSettle::getSettlementElectricFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 月度交易均价 = 月度交易电费 ÷ 月度交易电量
        BigDecimal monthlyAvgPrice = monthlyElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                monthlyFee.divide(monthlyElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        monthlyData.put("month", month);
        monthlyData.put("electricity", monthlyElectricity);  // 月度交易电量
        monthlyData.put("fee", monthlyFee);                  // 月度交易电费
        monthlyData.put("averagePrice", monthlyAvgPrice);    // 月度交易均价
        monthlyData.put("recordCount", monthlyRelations.size());

        return monthlyData;
    }

    /**
     * 构建月度交易信息结果（对所有字段进行求和）
     * 对power_side_settle表的所有数值字段进行求和
     */
    private Map<String, Object> buildMonthlyTradingResultWithAllFields(Long stationId, String yearMonth,
                                                                      Map<Long, List<PowerSideSettle>> fileSettleDataMap) {

        Map<String, Object> result = new HashMap<>();

        // 获取所有结算数据
        List<PowerSideSettle> allSettles = fileSettleDataMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 对所有字段进行求和
        BigDecimal totalActualInternetElectricity = allSettles.stream()
                .filter(settle -> settle.getActualInternetElectricity() != null)
                .map(PowerSideSettle::getActualInternetElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalSettlementElectricity = allSettles.stream()
                .filter(settle -> settle.getSettlementElectricity() != null)
                .map(PowerSideSettle::getSettlementElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalContractElectricity = allSettles.stream()
                .filter(settle -> settle.getContractElectricity() != null)
                .map(PowerSideSettle::getContractElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalDeviationElectricity = allSettles.stream()
                .filter(settle -> settle.getDeviationElectricity() != null)
                .map(PowerSideSettle::getDeviationElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalSettlementElectricFee = allSettles.stream()
                .filter(settle -> settle.getSettlementElectricFee() != null)
                .map(PowerSideSettle::getSettlementElectricFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算平均电价：总结算电费 ÷ 总结算电量
        BigDecimal averagePrice = totalSettlementElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                totalSettlementElectricFee.divide(totalSettlementElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        // 构建返回结果
        result.put("stationId", stationId);
        result.put("yearMonth", yearMonth);
        result.put("totalActualInternetElectricity", totalActualInternetElectricity);  // 实际上网电量总计
        result.put("totalSettlementElectricity", totalSettlementElectricity);          // 结算电量总计
        result.put("totalContractElectricity", totalContractElectricity);              // 合同电量总计
        result.put("totalDeviationElectricity", totalDeviationElectricity);            // 偏差电量总计
        result.put("totalSettlementElectricFee", totalSettlementElectricFee);          // 结算电费总计
        result.put("averagePrice", averagePrice);                                      // 平均电价
        result.put("fileCount", fileSettleDataMap.size());                            // 文件数量
        result.put("totalRecordCount", allSettles.size());                            // 总记录数

        log.info("电站月度交易信息构建完成 - 电站ID: {}, 年月: {}, 文件数: {}, " +
                "实际上网电量: {} MWh, 结算电量: {} MWh, 合同电量: {} MWh, 偏差电量: {} MWh, " +
                "结算电费: {} 万元, 平均电价: {} 元/MWh, 总记录数: {}",
                stationId, yearMonth, fileSettleDataMap.size(),
                totalActualInternetElectricity, totalSettlementElectricity, totalContractElectricity,
                totalDeviationElectricity, totalSettlementElectricFee, averagePrice, allSettles.size());

        return result;
    }
}