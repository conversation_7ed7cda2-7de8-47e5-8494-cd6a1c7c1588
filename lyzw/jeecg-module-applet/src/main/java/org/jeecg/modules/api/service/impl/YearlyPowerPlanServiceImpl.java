package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.mapper.YearlyPowerPlanMapper;
import org.jeecg.modules.api.power_trade.service.YearlyPowerPlanService;
import org.jeecg.modules.api.power_trade.dto.StationDetailDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 年度发电计划服务实现类
 */
@Service
public class YearlyPowerPlanServiceImpl extends ServiceImpl<YearlyPowerPlanMapper, YearlyPowerPlan> implements YearlyPowerPlanService {

    @Override
    public Map<String, Object> getStationYearlyPowerStats(Integer stationId, String year) {
        return baseMapper.getStationYearlyPowerStats(stationId, year);
    }

    @Override
    public StationDetailDTO getStationDetailWithPowerPlan(Long stationId, String year) {
        return baseMapper.getStationDetailWithPowerPlan(stationId, year);
    }

    @Override
    public List<YearlyPowerPlan> getStationMonthlyPowerPlan(Long stationId, String year) {
        return baseMapper.getStationMonthlyPowerPlan(stationId, year);
    }
}