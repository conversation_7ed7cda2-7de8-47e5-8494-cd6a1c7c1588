package org.jeecg.modules.api.power_trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.api.power_trade.constant.StationConstants;
import org.jeecg.modules.api.power_trade.mapper.StationMapper;
import org.jeecg.modules.api.power_trade.service.StationAggregationService;
import org.jeecg.modules.api.power_trade.vo.PowerDashboardVO;
import org.jeecg.modules.api.power_trade.vo.StationTypeStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 电站数据聚合服务实现类
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class StationAggregationServiceImpl implements StationAggregationService {


}
