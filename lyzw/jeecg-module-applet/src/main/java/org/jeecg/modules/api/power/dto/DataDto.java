package org.jeecg.modules.api.power.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据传输对象，用于构建曲线数据
 */
@Data
public class DataDto {
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 时间列表
     */
    private List<String> times = new ArrayList<>();
    
    /**
     * 值列表
     */
    private List<Double> values = new ArrayList<>();
} 