package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;

import java.util.Date;
import java.util.List;

public interface TradeCalendarRecordMapper extends BaseMapper<TradeCalendarRecord> {

    /**根据日期范围查询交易日历记录
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param provinceId 省份ID，为空则查询所有
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> selectCalendarByDateRange(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("provinceId") Integer provinceId);

    /**
     * 根据日期查询交易日历记录
     * @param tradeDate 交易日期
     * @param provinceId 省份ID，为空则查询所有
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> selectCalendarByDate(
            @Param("tradeDate") Date tradeDate,
            @Param("provinceId") Integer provinceId);

    /**
     * 根据月份和省份ID高效查询交易日历记录
     * @param startDate  月份的开始日期
     * @param endDate    月份的结束日期
     * @param provinceId 省份ID，为空则查询所有
     * @return 交易日历记录列表，包含类型名称和省份名称
     */
    List<TradeCalendarRecord> selectCalendarByMonthAndProvince(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("provinceId") Integer provinceId);

    /**
     * 查询交易日历记录（关联标的日和交易类型）- 适用于安徽省
     * @param date 日期
     * @param provinceId 省份ID
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> selectCalendarWithTargetDateAndTradeType(
            @Param("date") String date,
            @Param("provinceId") Integer provinceId);
    
    /**
     * 查询交易日历记录（仅关联交易类型）- 适用于江苏省
     * @param date 日期
     * @param provinceId 省份ID
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> selectCalendarWithTradeType(
            @Param("date") String date,
            @Param("provinceId") Integer provinceId);
}