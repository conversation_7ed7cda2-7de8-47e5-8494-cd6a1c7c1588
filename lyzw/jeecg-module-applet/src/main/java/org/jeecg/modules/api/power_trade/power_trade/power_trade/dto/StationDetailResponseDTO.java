package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class StationDetailResponseDTO {

    @ApiModelProperty("基础信息")
    private BasicInfo basicInfo;

    @ApiModelProperty("发电量信息 - 基于功率预测数据计算")
    private PowerGenerationInfo powerGeneration;
    
    @Data
    public static class BasicInfo {
        @ApiModelProperty("电站类型")
        private String stationType;
        
        @ApiModelProperty("电站名称")
        private String stationName;
        
        @ApiModelProperty("电站容量(MW)")
        private BigDecimal capacity;

        @ApiModelProperty("省份id")
        private Integer provinceId;

        @ApiModelProperty("省份名称")
        private String provinceName;

        @ApiModelProperty("年计划发电量(MWh)")
        private BigDecimal yearPlanPowerGeneration;
        
        @ApiModelProperty("累计发电量(MWh)")
        private BigDecimal totalPowerGeneration;
        
        // 从电站列表接口获取的结算数据
        @ApiModelProperty("当月实际发电量")
        private BigDecimal currentMonthPower;
        
        @ApiModelProperty("当月计划发电量")
        private BigDecimal currentMonthPlanPower;
        
        @ApiModelProperty("结算均价")
        private BigDecimal settlementAveragePrice;
    }
    
    @Data
    public static class PowerGenerationInfo {
        @ApiModelProperty("当前查询周期发电量(MWh) - 基于功率预测实际功率计算")
        private BigDecimal currentPeriodGeneration;

        @ApiModelProperty("详细发电量数据 - 基于功率预测的实际功率数据，实际功率除以4得到发电量")
        private List<PowerGenerationDetailData> detailData;

        @ApiModelProperty("查询维度")
        private String dimension;

        @ApiModelProperty("查询日期")
        private String queryDate;
    }

    @Data
    public static class PowerGenerationDetailData {
        @ApiModelProperty("时间标签 - 日维度:时间点(如00:15), 月维度:日期(如01-15), 年维度:月份(如2025-01)")
        private String timeLabel;

        @ApiModelProperty("实际功率(MW) - 日维度时显示具体功率值，月/年维度时为null")
        private Double actualPower;

        @ApiModelProperty("发电量(MWh) - 日维度:时间点发电量, 月维度:每日发电量, 年维度:每月发电量")
        private Double generation;
    }
}