package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.dto.YearlyMarketTradeDTO;
import org.jeecg.modules.api.power_trade.entity.ScreenMarketTrade;

import java.util.List;

@Mapper
public interface ScreenMarketTradeMapper extends BaseMapper<ScreenMarketTrade> {

    /**
     * 按年获取市场交易统计数据
     * @param year 年份
     * @param type 数据类型（可选）
     * @return 年度交易统计列表
     */
    List<YearlyMarketTradeDTO> getYearlyMarketTradeStats(@Param("year") String year, @Param("type") String type);

    /**
     * 获取所有年份的交易统计数据
     * @param type 数据类型（可选）
     * @return 年度交易统计列表
     */
    List<YearlyMarketTradeDTO> getAllYearlyMarketTradeStats(@Param("type") String type);

    /**
     * 按年份和类型获取月度明细数据
     * @param year 年份
     * @param type 数据类型（可选）
     * @return 月度交易数据列表
     */
    List<ScreenMarketTrade> getMonthlyTradeDetails(@Param("year") String year, @Param("type") String type);
}