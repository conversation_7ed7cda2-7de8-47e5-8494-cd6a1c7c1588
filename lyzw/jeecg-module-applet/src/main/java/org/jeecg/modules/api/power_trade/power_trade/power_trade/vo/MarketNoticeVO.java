package org.jeecg.modules.api.power_trade.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.api.power_trade.entity.NoticeFile;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * 市场公告VO - 前端展示
 */
@Data
@ApiModel(value = "MarketNoticeVO", description = "市场公告展示信息")
public class MarketNoticeVO {

    @ApiModelProperty(value = "公告ID")
    private Long id;

    @ApiModelProperty(value = "公告标题")
    private String noticeTitle;

    @ApiModelProperty(value = "公告类型ID")
    private Integer typeId;

    @ApiModelProperty(value = "公告类型名称")
    private String typeName;

    @ApiModelProperty(value = "披露日期")
    private Date noticeDate;

    @ApiModelProperty(value = "公告标签")
    private String noticeLabel;

    @ApiModelProperty(value = "公告内容")
    private String noticeContent;

    @ApiModelProperty(value = "公告摘要")
    private String summary;

    @ApiModelProperty(value = "浏览次数")
    private Integer viewCount;

    @ApiModelProperty(value = "发布者")
    private String publisher;

    @ApiModelProperty(value = "封面图片URL")
    private String coverUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "附件列表")
    private List<NoticeFile> attachments;

    @ApiModelProperty(value = "附件数量")
    private Integer attachmentCount;

    @ApiModelProperty(value = "标签列表")
    private List<String> tags;

    @ApiModelProperty(value = "预览url")
    private String fileExistUrl;

}
