package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyNewDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.EnergyNewDailyCleanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.List;

@Slf4j
@Service
public class EnergyNewDailyCleanServiceImpl extends ServiceImpl<EnergyNewDailyCleanMapper, EnergyNewDailyClean> implements EnergyNewDailyCleanService {

    @Override
    public List<EnergyNewDailyClean> selectByDay(Long stationId, String date) {
        try {
            List<EnergyNewDailyClean> result = baseMapper.selectByDay(stationId, date);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("按日查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyNewDailyClean> selectByMonth(Long stationId, String date) {
        try {
            log.info("开始按月查询新能源日清分数据 - 电站ID: {}, 日期: {}", stationId, date);
            List<EnergyNewDailyClean> result = baseMapper.selectByMonth(stationId, date);
            log.info("按月查询完成 - 电站ID: {}, 日期: {}, 原始结果数量: {}",
                    stationId, date, result != null ? result.size() : 0);

            // 补全月度数据，确保每一天都有记录
            List<EnergyNewDailyClean> completeResult = fillMissingDaysInMonth(stationId, date, result);

            log.info("数据补全完成 - 电站ID: {}, 日期: {}, 补全后数量: {}",
                    stationId, date, completeResult.size());

            return completeResult;
        } catch (Exception e) {
            log.error("按月查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyNewDailyClean> selectByYear(Long stationId, String date) {
        try {
            List<EnergyNewDailyClean> result = baseMapper.selectByYear(stationId, date);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("按年查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    /**
     * 补全月度数据，确保每一天都有记录
     * 如果某天没有数据，则创建一个默认的0值记录
     */
    private List<EnergyNewDailyClean> fillMissingDaysInMonth(Long stationId, String yearMonth, List<EnergyNewDailyClean> existingData) {
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 获取该月的天数
            YearMonth ym = YearMonth.of(year, month);
            int daysInMonth = ym.lengthOfMonth();

            // 创建日期到数据的映射
            Map<String, EnergyNewDailyClean> dataMap = new HashMap<>();
            if (existingData != null) {
                for (EnergyNewDailyClean data : existingData) {
                    if (data.getDate() != null) {
                        String dateKey = data.getDate().toString();
                        dataMap.put(dateKey, data);
                    }
                }
            }

            // 生成完整的月度数据
            List<EnergyNewDailyClean> completeData = new ArrayList<>();
            for (int day = 1; day <= daysInMonth; day++) {
                String dateStr = String.format("%04d-%02d-%02d", year, month, day);
                LocalDate currentDate = LocalDate.parse(dateStr);

                EnergyNewDailyClean dayData = dataMap.get(dateStr);
                if (dayData != null) {
                    // 如果有数据，直接使用
                    completeData.add(dayData);
                } else {
                    // 如果没有数据，创建默认的0值记录
                    EnergyNewDailyClean defaultData = createDefaultEnergyNewDailyClean(stationId, currentDate);
                    completeData.add(defaultData);
                }
            }

            log.debug("月度数据补全完成 - 年月: {}, 原始数据: {}条, 补全后: {}条",
                    yearMonth, existingData != null ? existingData.size() : 0, completeData.size());

            return completeData;

        } catch (Exception e) {
            log.error("补全月度数据失败 - 年月: {}", yearMonth, e);
            return existingData != null ? existingData : new ArrayList<>();
        }
    }

    /**
     * 创建默认的EnergyNewDailyClean记录（所有数值字段为0）
     */
    private EnergyNewDailyClean createDefaultEnergyNewDailyClean(Long stationId, LocalDate localDate) {
        EnergyNewDailyClean defaultData = new EnergyNewDailyClean();
        defaultData.setStationId(stationId);

        // 将LocalDate转换为Date
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        defaultData.setDate(date);

        // 设置所有数值字段为0
        defaultData.setMidLongTermPower(BigDecimal.ZERO);
        defaultData.setMidLongTermPrice(BigDecimal.ZERO);
        defaultData.setMidLongTermFee(BigDecimal.ZERO);
        defaultData.setGuaranteePower(BigDecimal.ZERO);
        defaultData.setGuaranteePrice(BigDecimal.ZERO);
        defaultData.setGuaranteeFee(BigDecimal.ZERO);
        defaultData.setDayAheadDeviationPower(BigDecimal.ZERO);
        defaultData.setDayAheadDeviationPrice(BigDecimal.ZERO);
        defaultData.setDayAheadDeviationFee(BigDecimal.ZERO);
        defaultData.setRealtimeDeviationPower(BigDecimal.ZERO);
        defaultData.setRealtimeDeviationPrice(BigDecimal.ZERO);
        defaultData.setRealtimeDeviationFee(BigDecimal.ZERO);
        defaultData.setExcessProfitRecovery(BigDecimal.ZERO);
        defaultData.setDayAheadProfitRecovery(BigDecimal.ZERO);
        defaultData.setTotalPower(BigDecimal.ZERO);
        defaultData.setTotalFee(BigDecimal.ZERO);
        defaultData.setSettlementAvgPrice(BigDecimal.ZERO);

        defaultData.setCreateTime(new Date());
        defaultData.setUpdateTime(new Date());
        return defaultData;
    }
}
