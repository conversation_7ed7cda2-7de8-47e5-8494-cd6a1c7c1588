package org.jeecg.modules.api.power_trade.param;

import lombok.Data;

import java.util.Date;

@Data
public class MarketNoticeQueryParam {

    /**
     * 当前页
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型ID
     */
    private Integer typeId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 公告标签
     */
    private String noticeLabel;
} 