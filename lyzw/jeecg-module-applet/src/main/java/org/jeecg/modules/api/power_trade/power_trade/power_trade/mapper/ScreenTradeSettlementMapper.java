package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 交易结算信息Mapper
 */
@Mapper
public interface ScreenTradeSettlementMapper extends BaseMapper<ScreenTradeSettlement> {

    /**
     * 根据电站ID和月份查询结算数据
     * @param stationId 电站ID
     * @param year 年份
     * @param month 月份
     * @return 结算数据
     */
    ScreenTradeSettlement selectByStationAndMonth(@Param("stationId") Long stationId,
                                                  @Param("year") String year,
                                                  @Param("month") String month);

    /**
     * 查询电站累计发电量（从年初到指定月份）
     * @param stationId 电站ID
     * @param endMonth 截止月份 (格式: yyyy-MM)
     * @return 累计发电量
     */
    BigDecimal selectCumulativePower(@Param("stationId") Long stationId,
                                     @Param("endMonth") String endMonth);

    /**
     * 批量查询电站结算数据
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份
     * @return 结算数据列表
     */
    @Select("<script>" +
            "SELECT * FROM screen_trade_settlement " +
            "WHERE year = #{year} AND month = #{month} " +
            "<if test='stationIds != null and stationIds.size() > 0'>" +
            "AND station_id IN " +
            "<foreach collection='stationIds' item='stationId' open='(' separator=',' close=')'>" +
            "#{stationId}" +
            "</foreach>" +
            "</if>" +
            "ORDER BY station_id" +
            "</script>")
    List<ScreenTradeSettlement> selectByStationIds(@Param("stationIds") List<Long> stationIds,
                                                   @Param("year") String year,
                                                   @Param("month") String month);

    /**
     * 查询电站指定月份的结算数据（用于电站总览）
     * @param stationId 电站ID
     * @param month 月份 (格式: yyyy-MM)
     * @return 结算数据
     */
    ScreenTradeSettlement selectByStationAndMonthForOverview(@Param("stationId") Long stationId,
                                                             @Param("month") String month);

    /**
     * 查询电站年度累计发电量
     * @param stationId 电站ID
     * @param year 年份
     * @return 年度累计发电量
     */
    BigDecimal selectYearCumulativePower(@Param("stationId") Long stationId,
                                         @Param("year") String year);

    /**
     * 查询指定电站、年份、月份的结算电量
     */
    BigDecimal selectSettlePower(@Param("stationId") Long stationId, @Param("year") String year, @Param("month") String month);

    /**
     * 查询指定电站、年份、月份的结算均价
     */
    BigDecimal selectSettlementAveragePrice(@Param("stationId") Long stationId, @Param("year") String year, @Param("month") String month);

    /**
     * 查询指定电站、年份、月份的标杆电价（目标电价）
     */
    BigDecimal selectBenchMarkElectricityPrice(@Param("stationId") Long stationId, @Param("year") String year, @Param("month") String month);

    /**
     * 批量获取电站聚合结算数据（用于电站总览）
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份（可选）
     * @return 电站聚合数据列表
     */
    List<Map<String, Object>> selectStationAggregatedData(@Param("stationIds") List<Long> stationIds,
                                                          @Param("year") String year,
                                                          @Param("month") String month);

    /**
     * 获取单个电站聚合结算数据
     * @param stationId 电站ID
     * @param year 年份
     * @param month 月份（可选）
     * @return 聚合数据
     */
    Map<String, Object> selectSingleStationAggregatedData(@Param("stationId") Long stationId,
                                                          @Param("year") String year,
                                                          @Param("month") String month);
    
    /**
     * 获取电站列表聚合结算数据
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份
     * @return 聚合结算数据
     */
    Map<String, Object> selectStationListAggregatedData(@Param("stationIds") List<Long> stationIds,
                                                        @Param("year") String year,
                                                        @Param("month") String month);

    /**
     * 获取首页概览数据 - 按省份聚合所有电站的最新结算数据
     * @param stationIds 电站ID列表
     * @return 首页概览数据
     */
    Map<String, Object> selectDashboardSummaryData(@Param("stationIds") List<Long> stationIds);

    /**
     * 获取首页概览数据 - 指定年月
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份
     * @return 首页概览数据
     */
    Map<String, Object> selectDashboardSummaryDataByMonth(@Param("stationIds") List<Long> stationIds,
                                                          @Param("year") String year,
                                                          @Param("month") String month);

    /**
     * 调试方法：检查表中的数据情况
     */
    Map<String, Object> debugTableData(@Param("stationIds") List<Long> stationIds);

    /**
     * 调试方法：查看具体的数据记录
     */
    List<Map<String, Object>> debugDetailData(@Param("stationIds") List<Long> stationIds);
}