package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("energy_storage_daily_clean")
public class EnergyStorageDailyClean {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("date")
    @ApiModelProperty(value = "日期", example = "2023-01-01")
    private Date date;

    @TableField("station_id")
    @ApiModelProperty(value = "电站ID", example = "1001")
    private Long stationId;

    @TableField("user_day_ahead_deviation_power")
    @ApiModelProperty(value = "用户日前偏差电量(单位:万千瓦时)", example = "10.12345")
    private BigDecimal userDayAheadDeviationPower;

    @TableField("user_day_ahead_deviation_average_price")
    @ApiModelProperty(value = "用户日前平均电价(单位:元/千瓦时)", example = "0.5")
    private BigDecimal userDayAheadDeviationAveragePrice;

    @TableField("user_day_ahead_deviation_fee")
    @ApiModelProperty(value = "用户日前电费(单位:万元)", example = "5.12345")
    private BigDecimal userDayAheadDeviationFee;

    @TableField("user_realtime_deviation_power")
    @ApiModelProperty(value = "用户实时偏差电量(单位:万千瓦时)", example = "5.12345")
    private BigDecimal userRealtimeDeviationPower;

    @TableField("user_realtime_deviation_average_price")
    @ApiModelProperty(value = "用户实时平均单价(单位:元/千瓦时)", example = "0.3")
    private BigDecimal userRealtimeDeviationAveragePrice;

    @TableField("user_realtime_deviation_fee")
    @ApiModelProperty(value = "用户实时电费(单位:万元)", example = "1.56173")
    private BigDecimal userRealtimeDeviationFee;

    @TableField("user_total_power")
    @ApiModelProperty(value = "用户用电总电量(单位:万千瓦时)", example = "95.12345")
    private BigDecimal userTotalPower;

    @TableField("user_total_fee")
    @ApiModelProperty(value = "用户用电总费用(单位:万元)", example = "47.56173")
    private BigDecimal userTotalFee;

    @TableField("power_generation_day_ahead_deviation_power")
    @ApiModelProperty(value = "发电日前偏差电量(单位:万千瓦时)", example = "8.12345")
    private BigDecimal powerGenerationDayAheadDeviationPower;

    @TableField("power_generation_day_ahead_deviation_average_price")
    @ApiModelProperty(value = "发电日前平均电价(单位:元/千瓦时)", example = "0.4")
    private BigDecimal powerGenerationDayAheadDeviationAveragePrice;

    @TableField("power_generation_day_ahead_deviation_fee")
    @ApiModelProperty(value = "发电日前电费(单位:万元)", example = "3.24938")
    private BigDecimal powerGenerationDayAheadDeviationFee;

    @TableField("power_generation_realtime_deviation_power")
    @ApiModelProperty(value = "发电实时偏差电量(单位:万千瓦时)", example = "4.12345")
    private BigDecimal powerGenerationRealtimeDeviationPower;

    @TableField("power_generation_realtime_deviation_average_price")
    @ApiModelProperty(value = "发电实时平均单价(单位:元/千瓦时)", example = "0.2")
    private BigDecimal powerGenerationRealtimeDeviationAveragePrice;

    @TableField("power_generation_realtime_deviation_fee")
    @ApiModelProperty(value = "发电实时电费(单位:万元)", example = "0.82469")
    private BigDecimal powerGenerationRealtimeDeviationFee;

    @TableField("power_generation_total_power")
    @ApiModelProperty(value = "发电总电量(单位:万千瓦时)", example = "90.12345")
    private BigDecimal powerGenerationTotalPower;

    @TableField("power_generation_total_fee")
    @ApiModelProperty(value = "发电总费用(单位:万元)", example = "36.09876")
    private BigDecimal powerGenerationTotalFee;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    /**
     * 省份ID - 用于全国汇总时标记数据来源省份
     * 注意：此字段不对应数据库表字段，仅用于业务逻辑处理
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "省份ID", example = "1")
    private Integer provinceId;

    /**
     * 交易日期 - 用于排序的日期字段
     * 注意：此字段不对应数据库表字段，仅用于业务逻辑处理
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "交易日期", example = "2024-01-15")
    private String tradeDate;
}