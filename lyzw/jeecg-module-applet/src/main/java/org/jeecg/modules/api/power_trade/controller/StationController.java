package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.realm.CachingRealm;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.util.NullValueHandler;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;

/**
 * 电力交易电站控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-电站接口")
@RequestMapping("/api/power_trade/stations")
public class StationController {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private DayAheadClearPowerService dayAheadClearPowerService;

    @Autowired
    private EnergyNewDailyCleanService energyNewDailyCleanService;

    @Autowired
    private EnergyStorageDailyCleanService energyStorageDailyCleanService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    @Autowired
    private StationBusinessService stationBusinessService;

    @Autowired
    private CachingRealm cachingRealm;

    @GetMapping("/list")
    @ApiOperation(value = "电站交易总览", notes = "根据省份显示涉及交易场站列表，包含累计结算电量和交易均价")
    public Result<Map<String, Object>> getStationList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "省份ID (0-全国, 1-安徽, 2-江苏)", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "查询维度 (1-月度, 2-年度)", required = true) @RequestParam Integer dimension,
            @ApiParam(value = "年份 (默认当前年)") @RequestParam(required = false) String year,
            @ApiParam(value = "月份 (维度为月度时必填，格式：01-12，默认当前月)") @RequestParam(required = false) String month,
            @ApiParam(value = "电站名称搜索") @RequestParam(required = false) String name) {

        // 参数验证和默认值处理
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validatePagination(pageNo, pageSize)
                    .validateProvinceId(provinceId)
                    .validateQueryDimension(dimension);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        try {
            // 设置默认值
            if (year == null || year.trim().isEmpty()) {
                year = String.valueOf(java.time.LocalDate.now().getYear());
            }

            if (dimension == 1) { // 月度查询
                if (month == null || month.trim().isEmpty()) {
                    month = String.format("%02d", java.time.LocalDate.now().getMonthValue());
                }
                // 验证月份格式
                if (!month.matches("^(0[1-9]|1[0-2])$")) {
                    return Result.error("月份格式错误，应为：01-12");
                }
            } else { // 年度查询
                month = null; // 年度查询不需要月份
            }

            Map<String, Object> result = stationBusinessService.getStationTradingOverview(
                    pageNo, pageSize, provinceId, dimension, year, month, name);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取电站交易概况失败 - 省份ID: {}, 维度: {}", provinceId, dimension, e);
            return Result.error("获取电站交易概况失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "电站详情", notes = "获取电站详细信息，包括基础信息、发电量和交易信息")
    @GetMapping("/{id}/detail")
    public Result<StationDetailResponseDTO> getStationDetail(
            @PathVariable Long id,
            @RequestParam String provinceId,
            @RequestParam String date,
            @RequestParam(defaultValue = "1") String dimension) {
        try {
            // 参数验证
            Result<String> validationResult = ParamValidationUtil.validate(() -> {
                ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
                validator.validateStationId(id);
                String timeDimension = validator.validateTimeDimension(dimension);
                validator.validateDateFormat(date, timeDimension);
                return timeDimension;
            });
            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }
            String timeDimension = validationResult.getResult();

            StationDetailResponseDTO response = stationBusinessService.getStationDetail(
                    id, provinceId, date, timeDimension);
            return Result.OK(response);

        } catch (NumberFormatException e) {
            log.error("省份ID格式错误: {}", provinceId, e);
            return Result.error("省份ID格式错误");
        } catch (Exception e) {
            log.error("获取电站详情失败 - 电站ID: {}, 省份ID: {}", id, provinceId, e);
            return Result.error("获取电站详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取电站年度交易电量信息
     */
    @GetMapping("/{id}/yearly-trading-info")
    @ApiOperation(value = "获取电站年度交易电量信息", notes = "获取电站的年度交易信息，包含月度分解数据")
    public Result<Map<String, Object>> getStationYearlyTradingInfo(
            @ApiParam(value = "电站ID", required = true) @PathVariable Long id,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "年份", required = true) @RequestParam String year) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (year == null || !year.matches("\\d{4}")) {
                return Result.error("年份格式不正确，请使用yyyy格式");
            }
            Map<String, Object> result = new HashMap<>();

            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }
            DynamicDataSourceContextHolder.push(dsKey);

            log.info("查询电站年度交易电量信息 - 电站ID: {}, 省份ID: {}, 年份: {}", id, provinceId, year);

            // 验证电站是否属于该省份
            Station station = stationService.getById(id);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 查询年度交易电量信息
            Map<String, Object> tradingInfo = powerSideSettleService.getStationYearlyTradingInfo(id, year);

            // 添加额外信息
            result.put("stationInfo", station);
            result.put("tradingInfo", tradingInfo);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取电站年度交易电量信息失败 - 电站ID: {}, 年份: {}", id, year, e);
            return Result.error("获取年度交易电量信息失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }


    @ApiOperation(value = "日交易光伏和风电", notes = "根据日期和电站ID获取光伏和风电数据。传入具体日期时，返回该日期所在月份的所有日期数据")
    @GetMapping("/getEnergyNewDailyClean")
    public Result<List<EnergyNewDailyClean>> getEnergyNewDailyClean(
            @ApiParam(value = "电站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "日期参数 (yyyy-MM-dd格式，如2025-09-25，将返回2025年9月整月数据)", required = true) @RequestParam String date,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId) {
        try {
            // 参数验证
            if (stationId == null || stationId <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("日期参数不能为空");
            }

            // 验证日期格式并提取月份信息
            String targetMonth;
            if (date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                // 如果传入具体日期，提取年月部分
                targetMonth = date.substring(0, 7); // 提取yyyy-MM
                log.info("传入具体日期: {}, 提取目标月份: {}", date, targetMonth);
            } else if (date.matches("\\d{4}-\\d{2}")) {
                // 如果传入年月，直接使用
                targetMonth = date;
                log.info("传入年月: {}", targetMonth);
            } else {
                return Result.error("日期格式不正确，支持格式：yyyy-MM-dd（具体日期）或 yyyy-MM（年月）");
            }

            log.info("查询新能源日清分数据 - 电站ID: {}, 省份ID: {}, 原始日期: {}, 目标月份: {}",
                    stationId, provinceId, date, targetMonth);

            // 单省份模式
            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }

            DynamicDataSourceContextHolder.push(dsKey);

            // 记录当前数据源
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.info("当前数据源: {}", currentDataSource);

            // 验证电站是否属于该省份
            Station station = stationService.getById(stationId);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            log.info("电站验证成功 - 电站名称: {}, 电站省份ID: {}", station.getName(), station.getProvinceId());

            // 根据目标月份获取整月数据
            List<EnergyNewDailyClean> result = null;
            try {
                // 构建月份查询的开始和结束日期
                String startDate = targetMonth + "-01"; // 月初
                String endDate = getLastDayOfMonth(targetMonth); // 月末

                log.info("查询月份范围 - 开始日期: {}, 结束日期: {}", startDate, endDate);

                // 调用Service查询整月数据
                result = energyNewDailyCleanService.selectByMonth(stationId, targetMonth);

            } catch (Exception queryException) {
                log.error("数据库查询异常 - 电站ID: {}, 目标月份: {}",
                        stationId, targetMonth, queryException);
                // 返回空列表而不是抛出异常
                result = new ArrayList<>();
            }

            // 确保result不为null
            if (result == null) {
                result = new ArrayList<>();
            }

            log.info("查询成功 - 电站: {}, 目标月份: {}, 返回记录数: {}",
                    station.getName(), targetMonth, result.size());

            if (result.isEmpty()) {
                log.warn("未查询到数据 - 电站ID: {}, 目标月份: {}", stationId, targetMonth);
            } else {
                // 记录查询到的日期范围
                String firstDate = result.get(0).getDate() != null ? result.get(0).getDate().toString() : "未知";
                String lastDate = result.get(result.size() - 1).getDate() != null ?
                        result.get(result.size() - 1).getDate().toString() : "未知";
                log.info("数据日期范围 - 从: {} 到: {}", firstDate, lastDate);
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取指定年月的最后一天
     * @param yearMonth 年月，格式：yyyy-MM
     * @return 最后一天，格式：yyyy-MM-dd
     */
    private String getLastDayOfMonth(String yearMonth) {
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 使用YearMonth获取该月的最后一天
            YearMonth ym = YearMonth.of(year, month);
            LocalDate lastDay = ym.atEndOfMonth();

            return lastDay.toString(); // 返回yyyy-MM-dd格式

        } catch (Exception e) {
            log.error("解析年月失败: {}", yearMonth, e);
            // 如果解析失败，返回默认的月末日期
            return yearMonth + "-31";
        }
    }

    /**
     * 储能日清洁数据
     */
    @ApiOperation(value = "日交易储能", notes = "根据日期和电站ID获取储能数据。传入具体日期时，返回该日期所在月份的所有日期数据")
    @GetMapping("/getEnergyStorageDailyClean")
    public Result<List<EnergyStorageDailyClean>> getEnergyStorageDailyClean(
            @ApiParam(value = "电站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "日期参数 (yyyy-MM-dd格式，如2025-09-25，将返回2025年9月整月数据)", required = true) @RequestParam String date,
            @ApiParam(value = "省份ID (0-全国, 1-安徽, 2-江苏)", required = true) @RequestParam Integer provinceId) {

        try {
            // 参数验证
            if (stationId == null || stationId <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("日期参数不能为空");
            }

            // 验证日期格式并提取月份信息
            String targetMonth;
            if (date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                // 如果传入具体日期，提取年月部分
                targetMonth = date.substring(0, 7); // 提取yyyy-MM
                log.info("传入具体日期: {}, 提取目标月份: {}", date, targetMonth);
            } else if (date.matches("\\d{4}-\\d{2}")) {
                // 如果传入年月，直接使用
                targetMonth = date;
                log.info("传入年月: {}", targetMonth);
            } else {
                return Result.error("日期格式不正确，支持格式：yyyy-MM-dd（具体日期）或 yyyy-MM（年月）");
            }

            log.info("查询储能日清分数据 - 电站ID: {}, 省份ID: {}, 原始日期: {}, 目标月份: {}",
                    stationId, provinceId, date, targetMonth);

            // 单省份模式
            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }

            DynamicDataSourceContextHolder.push(dsKey);

            // 记录当前数据源
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.info("当前数据源: {}", currentDataSource);

            // 验证电站是否属于该省份
            Station station = stationService.getById(stationId);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            log.info("电站验证成功 - 电站名称: {}, 电站省份ID: {}", station.getName(), station.getProvinceId());

            // 根据目标月份获取整月数据
            List<EnergyStorageDailyClean> result = null;
            try {
                // 构建月份查询的开始和结束日期
                String startDate = targetMonth + "-01"; // 月初
                String endDate = getLastDayOfMonth(targetMonth); // 月末

                log.info("查询月份范围 - 开始日期: {}, 结束日期: {}", startDate, endDate);

                // 调用Service查询整月数据
                result = energyStorageDailyCleanService.selectByMonth(stationId, targetMonth);

            } catch (Exception queryException) {
                log.error("数据库查询异常 - 电站ID: {}, 目标月份: {}",
                        stationId, targetMonth, queryException);
                // 返回空列表而不是抛出异常
                result = new ArrayList<>();
            }

            // 确保result不为null
            if (result == null) {
                result = new ArrayList<>();
            }

            log.info("查询成功 - 电站: {}, 目标月份: {}, 返回记录数: {}",
                    station.getName(), targetMonth, result.size());

            if (result.isEmpty()) {
                log.warn("未查询到数据 - 电站ID: {}, 目标月份: {}", stationId, targetMonth);
            } else {
                // 记录查询到的日期范围
                String firstDate = result.get(0).getDate() != null ? result.get(0).getDate().toString() : "未知";
                String lastDate = result.get(result.size() - 1).getDate() != null ?
                        result.get(result.size() - 1).getDate().toString() : "未知";
                log.info("数据日期范围 - 从: {} 到: {}", firstDate, lastDate);
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询储能日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
}