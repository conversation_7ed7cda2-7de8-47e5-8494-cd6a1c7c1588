package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.Station;

import java.util.List;

/**
 * 电力交易电站服务接口
 */
public interface StationService extends IService<Station> {

    /**
     * 根据省份ID获取电站列表
     * @param provinceId 省份ID
     * @return 电站列表
     */
    List<Station> getStationsByProvinceId(Integer provinceId);

}