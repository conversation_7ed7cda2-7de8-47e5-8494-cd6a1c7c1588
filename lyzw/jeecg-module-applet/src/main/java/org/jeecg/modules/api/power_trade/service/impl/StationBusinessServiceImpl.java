package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.entity.RpJiaYue;
import org.jeecg.modules.api.power.mapper.JiaYueRpMapper;
import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.util.NullValueHandler;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 电站业务服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StationBusinessServiceImpl implements StationBusinessService {

    private final StationService stationService;
    private final PowerSideSettleService powerSideSettleService;
    private final EnergyStorageDailyCleanService energyStorageDailyCleanService;
    private final MultiDataSourceAggregationService multiDataSourceAggregationService;
    private final StationDetailHelper stationDetailHelper;
    private final JiaYueRpMapper jiaYueRpMapper;
    private final YearlyPowerPlanService yearlyPowerPlanService;
    private final PowerService powerService;
    private final ScreenTradeSettlementService screenTradeSettlementService;

    @Override
    public Map<String, Object> getStationList(Integer pageNo, Integer pageSize, Integer provinceId,
                                              String year, String month, String name) {
        log.info("开始获取电站列表 - 省份ID: {}, 页码: {}, 每页: {}", provinceId, pageNo, pageSize);

        try {
            return provinceId == 0
                    ? getNationalStationList(pageNo, pageSize, year, month, name)
                    : getSingleProvinceStationList(pageNo, pageSize, provinceId, year, month, name);

        } catch (Exception e) {
            log.error("获取电站列表失败 - 省份ID: {}", provinceId, e);
            return NullValueHandler.ensurePaginationResult(new HashMap<>());
        }
    }

    @Override
    public Map<String, Object> getNationalStationList(Integer pageNo, Integer pageSize,
                                                      String year, String month, String name) {
        log.info("开始获取全国汇总电站列表");

        try {
            // 使用现有的聚合服务
            Map<String, Object> aggregatedResult = multiDataSourceAggregationService
                    .aggregateAllProvincesStationList(pageNo, pageSize, year, month, name);

            return NullValueHandler.ensurePaginationResult(aggregatedResult);

        } catch (Exception e) {
            log.error("获取全国汇总电站列表失败", e);
            return NullValueHandler.ensurePaginationResult(new HashMap<>());
        }
    }

    @Override
    public Map<String, Object> getSingleProvinceStationList(Integer pageNo, Integer pageSize, Integer provinceId,
                                                            String year, String month, String name) {
        log.info("开始获取单省份电站列表 - 省份ID: {}", provinceId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 构建查询条件
                Page<Station> page = new Page<>(pageNo, pageSize);
                LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Station::getTradeStatus, 1)
                        .eq(Station::getProvinceId, provinceId);

                // 添加电站名称搜索条件
                if (name != null && !name.trim().isEmpty()) {
                    queryWrapper.like(Station::getName, name.trim());
                }

                // 执行分页查询
                IPage<Station> pageList = stationService.page(page, queryWrapper);

                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("records", pageList.getRecords());
                result.put("total", pageList.getTotal());
                result.put("size", pageList.getSize());
                result.put("current", pageList.getCurrent());
                result.put("pages", pageList.getPages());

                log.info("单省份电站列表查询完成 - 省份ID: {}, 总数: {}", provinceId, pageList.getTotal());
                return result;

            } catch (Exception e) {
                log.error("获取省份{}电站列表失败", provinceId, e);
                return NullValueHandler.ensurePaginationResult(new HashMap<>());
            }
        });
    }

    @Override
    public Map<String, Object> getStationTradingOverview(Integer pageNo, Integer pageSize, Integer provinceId,
                                                         Integer dimension, String year, String month, String name) {
        log.info("开始获取首页电站交易概况 - 省份ID: {}, 维度: {}, 年份: {}, 月份: {}", provinceId, dimension, year, month);

        try {
            return provinceId == 0
                    ? getNationalStationTradingOverview(pageNo, pageSize, dimension, year, month, name)
                    : getSingleProvinceStationTradingOverview(pageNo, pageSize, provinceId, dimension, year, month, name);

        } catch (Exception e) {
            log.error("获取首页电站交易概况失败 - 省份ID: {}, 维度: {}", provinceId, dimension, e);
            return NullValueHandler.ensurePaginationResult(new HashMap<>());
        }
    }

    /**
     * 获取全国所有省份的电站交易概况详细列表
     */
    private Map<String, Object> getNationalStationTradingOverview(Integer pageNo, Integer pageSize,
                                                                  Integer dimension, String year, String month, String name) {
        log.info("开始获取全国所有省份的电站交易概况详细列表");

        try {
            // 获取所有省份的数据源（排除provinceId=0）
            Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();
            List<Integer> supportedProvinces = allDataSources.keySet().stream()
                    .filter(provinceId -> provinceId != 0) // 排除全国汇总
                    .sorted()
                    .collect(Collectors.toList());

            // 收集所有省份的电站数据
            List<Map<String, Object>> allStations = new ArrayList<>();

            for (Integer provinceId : supportedProvinces) {
                try {
                    // 查询该省份的所有电站数据（不分页）
                    List<Map<String, Object>> provinceStations = queryProvinceStationTradingData(
                            provinceId, dimension, year, month, name);
                    allStations.addAll(provinceStations);

                    log.debug("省份{}查询到{}个电站", provinceId, provinceStations.size());

                } catch (Exception e) {
                    log.warn("查询省份{}电站数据失败: {}", provinceId, e.getMessage());
                    // 继续处理其他省份，不因单个省份失败而中断
                }
            }

            // 应用电站名称过滤（如果有）
            if (name != null && !name.trim().isEmpty()) {
                String searchName = name.trim().toLowerCase();
                allStations = allStations.stream()
                        .filter(station -> {
                            Map<String, Object> stationInfo = (Map<String, Object>) station.get("station");
                            if (stationInfo != null && stationInfo.get("name") != null) {
                                String stationName = stationInfo.get("name").toString().toLowerCase();
                                return stationName.contains(searchName);
                            }
                            return false;
                        })
                        .collect(Collectors.toList());
            }

            // 手动实现分页
            int total = allStations.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedStations = new ArrayList<>();
            if (startIndex < total) {
                pagedStations = allStations.subList(startIndex, endIndex);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedStations);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNo);
            result.put("pages", (total + pageSize - 1) / pageSize); // 向上取整

            log.info("全国电站交易概况查询完成 - 总电站数: {}, 当前页: {}, 每页: {}, 返回: {}",
                    total, pageNo, pageSize, pagedStations.size());

            return NullValueHandler.ensurePaginationResult(result);

        } catch (Exception e) {
            log.error("获取全国电站交易概况详细列表失败", e);
            return NullValueHandler.ensurePaginationResult(new HashMap<>());
        }
    }

    /**
     * 获取单省份电站交易概况
     */
    private Map<String, Object> getSingleProvinceStationTradingOverview(Integer pageNo, Integer pageSize, Integer provinceId,
                                                                        Integer dimension, String year, String month, String name) {
        log.info("开始获取单省份电站交易概况 - 省份ID: {}", provinceId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 构建查询条件
                Page<Station> page = new Page<>(pageNo, pageSize);
                LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Station::getTradeStatus, 1)
                        .eq(Station::getProvinceId, provinceId);

                // 添加电站名称搜索条件
                if (name != null && !name.trim().isEmpty()) {
                    queryWrapper.like(Station::getName, name.trim());
                }

                // 执行分页查询
                IPage<Station> pageList = stationService.page(page, queryWrapper);

                // 为每个电站添加结算数据
                List<Map<String, Object>> stationTradingList = pageList.getRecords().stream()
                        .map(station -> buildStationTradingOverview(station, dimension, year, month))
                        .collect(Collectors.toList());

                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("records", stationTradingList);
                result.put("total", pageList.getTotal());
                result.put("size", pageList.getSize());
                result.put("current", pageList.getCurrent());
                result.put("pages", pageList.getPages());

                log.info("单省份电站交易概况查询完成 - 省份ID: {}, 总数: {}", provinceId, pageList.getTotal());
                return result;

            } catch (Exception e) {
                log.error("获取省份{}电站交易概况失败", provinceId, e);
                return NullValueHandler.ensurePaginationResult(new HashMap<>());
            }
        });
    }


    @Override
    public StationDetailResponseDTO getStationDetail(Long id, String provinceId, String date, String dimension) {
        log.info("开始获取电站详情 - 电站ID: {}, 省份ID: {}", id, provinceId);

        try {
            Integer pId = Integer.valueOf(provinceId);
            return executeWithDataSource(pId, () -> {
                // 这里可以调用原有的buildStationDetailResponse方法
                // 或者重新实现电站详情构建逻辑
                return buildStationDetailResponse(id, date, dimension);
            });

        } catch (Exception e) {
            log.error("获取电站详情失败 - 电站ID: {}", id, e);
            throw new RuntimeException("获取电站详情失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getStationYearlyTradingInfo(Long id, Integer provinceId, String year) {
        log.info("开始获取电站年度交易电量信息 - 电站ID: {}, 省份ID: {}", id, provinceId);

        try {
            return provinceId == 0
                    ? getNationalStationYearlyTradingInfo(id, year)
                    : getSingleProvinceStationYearlyTradingInfo(id, provinceId, year);

        } catch (Exception e) {
            log.error("获取电站年度交易电量信息失败 - 电站ID: {}", id, e);
            return createDefaultTradingInfo(id, "年度");
        }
    }

    @Override
    public Map<String, Object> getNationalStationYearlyTradingInfo(Long id, String year) {
        log.info("开始获取全国汇总电站年度交易电量信息 - 电站ID: {}", id);

        try {
            // 获取所有省份数据源
            Map<Integer, String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();
            List<Integer> supportedProvinces = provinceDataSourceMap.keySet().stream()
                    .filter(provinceId -> provinceId != 0)
                    .sorted()
                    .collect(Collectors.toList());

            // 并行查询所有省份的年度交易数据
            List<Map<String, Object>> allResults = supportedProvinces.parallelStream()
                    .map(provinceId -> {
                        try {
                            Map<String, Object> provinceResult = getSingleProvinceStationYearlyTradingInfo(id, provinceId, year);
                            if (provinceResult != null && Boolean.TRUE.equals(provinceResult.get("hasData"))) {
                                return provinceResult;
                            }
                        } catch (Exception e) {
                            log.warn("查询省份{}的电站{}年度交易数据失败: {}", provinceId, id, e.getMessage());
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 聚合所有省份的数据
            return aggregateYearlyTradingData(allResults, id, year);

        } catch (Exception e) {
            log.error("获取全国汇总电站年度交易电量信息失败 - 电站ID: {}", id, e);
            return createDefaultTradingInfo(id, "年度");
        }
    }

    @Override
    public Map<String, Object> getSingleProvinceStationYearlyTradingInfo(Long id, Integer provinceId, String year) {
        log.info("开始获取单省份电站年度交易电量信息 - 电站ID: {}, 省份ID: {}", id, provinceId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 验证电站是否存在
                Station station = stationService.getById(id);
                if (station == null) {
                    throw new RuntimeException("电站不存在");
                }

                // 查询年度交易电量信息
                Map<String, Object> tradingInfo = powerSideSettleService.getStationYearlyTradingInfo(id, year);

                // 添加额外信息
                tradingInfo.put("stationId", id);
                tradingInfo.put("stationName", station.getName());
                tradingInfo.put("provinceId", provinceId);
                tradingInfo.put("hasData", tradingInfo.get("monthlyData") != null &&
                        !((List<?>) tradingInfo.get("monthlyData")).isEmpty());

                log.info("单省份年度交易电量查询成功 - 电站ID: {}, 省份ID: {}", id, provinceId);
                return tradingInfo;

            } catch (Exception e) {
                log.error("获取省份{}电站{}年度交易电量信息失败", provinceId, id, e);
                return createDefaultTradingInfo(id, "年度");
            }
        });
    }


    @Override
    public Map<String, Object> getStationMonthlyTradingInfo(Long id, Integer provinceId, String yearMonth) {
        log.info("开始获取电站月度交易电量信息 - 电站ID: {}, 省份ID: {}", id, provinceId);

        try {
            return provinceId == 0
                    ? getNationalStationMonthlyTradingInfo(id, yearMonth)
                    : getSingleProvinceStationMonthlyTradingInfo(id, provinceId, yearMonth);

        } catch (Exception e) {
            log.error("获取电站月度交易电量信息失败 - 电站ID: {}", id, e);
            return createDefaultTradingInfo(id, "月度");
        }
    }

    @Override
    public Map<String, Object> getNationalStationMonthlyTradingInfo(Long id, String yearMonth) {
        log.info("开始获取全国汇总电站月度交易电量信息 - 电站ID: {}", id);

        try {
            // 获取所有省份数据源
            Map<Integer, String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();
            List<Integer> supportedProvinces = provinceDataSourceMap.keySet().stream()
                    .filter(provinceId -> provinceId != 0)
                    .sorted()
                    .collect(Collectors.toList());

            // 并行查询所有省份的月度交易数据
            List<Map<String, Object>> allResults = supportedProvinces.parallelStream()
                    .map(provinceId -> {
                        try {
                            Map<String, Object> provinceResult = getSingleProvinceStationMonthlyTradingInfo(id, provinceId, yearMonth);
                            if (provinceResult != null && Boolean.TRUE.equals(provinceResult.get("hasData"))) {
                                return provinceResult;
                            }
                        } catch (Exception e) {
                            log.warn("查询省份{}的电站{}月度交易数据失败: {}", provinceId, id, e.getMessage());
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 聚合所有省份的数据
            return aggregateMonthlyTradingData(allResults, id, yearMonth);

        } catch (Exception e) {
            log.error("获取全国汇总电站月度交易电量信息失败 - 电站ID: {}", id, e);
            return createDefaultTradingInfo(id, "月度");
        }
    }

    @Override
    public Map<String, Object> getSingleProvinceStationMonthlyTradingInfo(Long id, Integer provinceId, String yearMonth) {
        log.info("开始获取单省份电站月度交易电量信息 - 电站ID: {}, 省份ID: {}", id, provinceId);

        return executeWithDataSource(provinceId, () -> {
            try {
                // 验证电站是否存在
                Station station = stationService.getById(id);
                if (station == null) {
                    throw new RuntimeException("电站不存在");
                }
                if (!station.getProvinceId().equals(provinceId)) {
                    throw new RuntimeException("电站不属于指定省份");
                }

                // 查询月度交易电量信息
                Map<String, Object> tradingInfo = powerSideSettleService.getStationMonthlyTradingInfo(id, yearMonth);

                // 添加额外信息
                tradingInfo.put("stationId", id);
                tradingInfo.put("stationName", station.getName());
                tradingInfo.put("provinceId", provinceId);
                tradingInfo.put("hasData", tradingInfo.get("monthlyTotalElectricity") != null);

                log.info("单省份月度交易电量查询成功 - 电站ID: {}, 省份ID: {}", id, provinceId);
                return tradingInfo;

            } catch (Exception e) {
                log.error("获取省份{}电站{}月度交易电量信息失败", provinceId, id, e);
                return createDefaultTradingInfo(id, "月度");
            }
        });
    }

    // ==================== 储能日清洁数据相关方法 ====================

    @Override
    public List<EnergyStorageDailyClean> getEnergyStorageDailyClean(String provinceId, String stationId,
                                                                    String date) {
        log.info("开始获取储能日清洁数据 - 省份ID: {}, 电站ID: {}", provinceId, stationId);

        try {
            return "0".equals(provinceId)
                    ? getNationalEnergyStorageDailyClean(stationId, date)
                    : getSingleProvinceEnergyStorageDailyClean(provinceId, stationId, date, "day");

        } catch (Exception e) {
            log.error("获取储能日清洁数据失败 - 省份ID: {}, 电站ID: {}", provinceId, stationId, e);
            return new ArrayList<>();
        }
    }

    public List<EnergyStorageDailyClean> getNationalEnergyStorageDailyClean(String stationId, String date) {
        log.info("开始获取全国汇总储能日清洁数据 - 电站ID: {}", stationId);

        try {
            // 获取所有省份数据源
            Map<Integer, String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();
            List<Integer> supportedProvinces = provinceDataSourceMap.keySet().stream()
                    .filter(provinceId -> provinceId != 0)  // 排除全国汇总
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());

            log.info("开始并行查询{}个省份的储能数据", supportedProvinces.size());

            // 使用并行流收集所有省份的储能数据
            List<EnergyStorageDailyClean> allRecords = supportedProvinces.parallelStream()
                    .flatMap(provinceId -> {
                        try {
                            List<EnergyStorageDailyClean> provinceRecords = getSingleProvinceEnergyStorageDailyClean(
                                    String.valueOf(provinceId), stationId, date, "day");

                            if (!provinceRecords.isEmpty()) {
                                // 使用流式操作标记省份信息并收集记录
                                return provinceRecords.stream()
                                        .peek(record -> {
                                            if (record.getProvinceId() == null) {
                                                record.setProvinceId(provinceId);
                                            }
                                        });
                            }
                            return Stream.empty();

                        } catch (Exception e) {
                            log.warn("查询省份{}的储能数据失败: {}", provinceId, e.getMessage());
                            // 继续查询其他省份，不因为一个省份失败而中断
                            return Stream.empty();
                        }
                    })
                    .sorted(Comparator.comparing(
                            EnergyStorageDailyClean::getDate,
                            Comparator.nullsLast(Comparator.naturalOrder())
                    ))
                    .collect(Collectors.toList());

            log.info("全国汇总储能日清洁数据查询完成 - 总记录数: {}", allRecords.size());
            return allRecords;

        } catch (Exception e) {
            log.error("获取全国汇总储能日清洁数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 在指定数据源上下文中执行操作
     */
    private <T> T executeWithDataSource(Integer provinceId, Supplier<T> operation) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            throw new IllegalArgumentException("不支持的省份ID: " + provinceId);
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            return operation.get();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 创建默认交易信息
     */
    private Map<String, Object> createDefaultTradingInfo(Long stationId, String type) {
        Map<String, Object> defaultInfo = new HashMap<>();
        defaultInfo.put("stationId", stationId);
        defaultInfo.put("hasData", false);
        defaultInfo.put("type", type);
        defaultInfo.put("yearlyTotalElectricity", BigDecimal.ZERO);
        defaultInfo.put("yearlyAveragePrice", BigDecimal.ZERO);
        defaultInfo.put("monthlyData", new ArrayList<>());
        return defaultInfo;
    }

    /**
     * 聚合年度交易数据
     */
    private Map<String, Object> aggregateYearlyTradingData(Collection<Map<String, Object>> allResults, Long stationId, String year) {
        // 实现年度数据聚合逻辑
        Map<String, Object> aggregated = new HashMap<>();
        aggregated.put("stationId", stationId);
        aggregated.put("year", year);
        aggregated.put("hasData", !allResults.isEmpty());

        // 这里可以实现具体的聚合逻辑
        BigDecimal totalElectricity = allResults.stream()
                .map(result -> (BigDecimal) result.get("yearlyTotalElectricity"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        aggregated.put("yearlyTotalElectricity", totalElectricity);
        aggregated.put("provinceCount", allResults.size());

        return aggregated;
    }

    /**
     * 聚合月度交易数据
     */
    private Map<String, Object> aggregateMonthlyTradingData(Collection<Map<String, Object>> allResults, Long stationId, String yearMonth) {
        // 实现月度数据聚合逻辑
        Map<String, Object> aggregated = new HashMap<>();
        aggregated.put("stationId", stationId);
        aggregated.put("yearMonth", yearMonth);
        aggregated.put("hasData", !allResults.isEmpty());

        // 这里可以实现具体的聚合逻辑
        BigDecimal totalElectricity = allResults.stream()
                .map(result -> (BigDecimal) result.get("monthlyTotalElectricity"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        aggregated.put("monthlyTotalElectricity", totalElectricity);
        aggregated.put("provinceCount", allResults.size());

        return aggregated;
    }

    /**
     * 构建电站详情响应 - 简化版本，只返回基础信息和发电量
     */
    private StationDetailResponseDTO buildStationDetailResponse(Long id, String date, String dimension) {
        log.info("开始构建电站详情响应 - 电站ID: {}, 日期: {}, 维度: {}", id, date, dimension);

        try {
            StationDetailResponseDTO response = new StationDetailResponseDTO();

            // 1. 获取基础信息
            response.setBasicInfo(buildBasicInfo(id, date));

            // 2. 获取发电量信息（基于功率预测数据）
            response.setPowerGeneration(buildPowerGenerationInfoFromForecast(id, date, dimension));

            log.info("电站详情响应构建完成 - 电站ID: {}", id);
            return response;

        } catch (Exception e) {
            log.error("构建电站详情响应失败 - 电站ID: {}", id, e);
            // 返回默认的响应，避免null值
            return stationDetailHelper.buildDefaultStationDetailResponse(id, date, dimension);
        }
    }

    /**
     * 构建基础信息
     */
    private StationDetailResponseDTO.BasicInfo buildBasicInfo(Long stationId, String date) {
        try {
            log.info("开始构建电站基础信息 - 电站ID: {}, 日期: {}", stationId, date);

            // 获取电站基本信息
            Station station = stationService.getById(stationId);
            if (station == null) {
                log.warn("电站不存在 - 电站ID: {}", stationId);
                return stationDetailHelper.buildDefaultBasicInfo(stationId);
            }

            log.info("电站信息查询成功 - 电站名称: {}, 类型: {}, 容量: {}, 省份ID: {}",
                    station.getName(), station.getType(), station.getCapacity(), station.getProvinceId());

            StationDetailResponseDTO.BasicInfo basicInfo = new StationDetailResponseDTO.BasicInfo();

            // 设置基本信息
            basicInfo.setStation(station);

            // 获取年度发电计划
            String year = date.length() >= 4 ? date.substring(0, 4) : "2024";


            // 尝试获取年度计划发电量（可以从其他表或配置中获取）
            BigDecimal yearPlanGeneration = getYearPlanPowerGeneration(stationId, year);
            basicInfo.setYearPlanPowerGeneration(yearPlanGeneration);

            // 计算累计发电量
            BigDecimal totalPowerGeneration = stationDetailHelper.calculateTotalPowerGeneration(stationId, year);
            basicInfo.setTotalPowerGeneration(totalPowerGeneration);

            log.info("电站基础信息构建完成 - 电站ID: {}, 累计发电量: {}, 当月电量: {}, 结算均价: {}",
                    stationId, totalPowerGeneration, basicInfo.getCurrentMonthPower(), basicInfo.getSettlementAveragePrice());

            return basicInfo;

        } catch (Exception e) {
            log.error("构建基础信息失败 - 电站ID: {}", stationId, e);
            return stationDetailHelper.buildDefaultBasicInfo(stationId);
        }
    }

    /**
     * 获取年度计划发电量
     */
    private BigDecimal getYearPlanPowerGeneration(Long stationId, String year) {
        try {
            LambdaQueryWrapper<YearlyPowerPlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(YearlyPowerPlan::getStationId, stationId);
            queryWrapper.eq(YearlyPowerPlan::getYear, year);
            List<YearlyPowerPlan> yearlyPowerPlans = yearlyPowerPlanService.list(queryWrapper);
            return yearlyPowerPlans.stream()
                    .filter(Objects::nonNull)
                    .map(YearlyPowerPlan::getPlanValue)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.warn("获取年度计划发电量失败 - 电站ID: {}", stationId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 构建基于功率预测的发电量信息
     */
    private StationDetailResponseDTO.PowerGenerationInfo buildPowerGenerationInfoFromForecast(Long stationId, String date, String dimension) {
        try {
            StationDetailResponseDTO.PowerGenerationInfo powerGeneration = new StationDetailResponseDTO.PowerGenerationInfo();

            powerGeneration.setDimension(dimension);
            powerGeneration.setQueryDate(date);

            // 根据维度获取功率预测数据并计算发电量
            BigDecimal currentPeriodGeneration = BigDecimal.ZERO;
            List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();

            switch (dimension) {
                case "1": // 年维度 - 返回该年每一个月的数据
                    Map<String, Object> yearlyData = getPowerForecastYearlyData(stationId, date);
                    currentPeriodGeneration = (BigDecimal) yearlyData.getOrDefault("totalGeneration", BigDecimal.ZERO);
                    detailData = (List<StationDetailResponseDTO.PowerGenerationDetailData>)
                            yearlyData.getOrDefault("detailData", new ArrayList<>());
                    break;
                case "2": // 月维度 - 返回该月每一天的数据
                    Map<String, Object> monthlyData = getPowerForecastMonthlyData(stationId, date);
                    currentPeriodGeneration = (BigDecimal) monthlyData.getOrDefault("totalGeneration", BigDecimal.ZERO);
                    detailData = (List<StationDetailResponseDTO.PowerGenerationDetailData>)
                            monthlyData.getOrDefault("detailData", new ArrayList<>());
                    break;
                case "3": // 日维度 - 返回当天96个时间点的数据
                    Map<String, Object> dailyData = getPowerForecastDailyData(stationId, date);
                    currentPeriodGeneration = (BigDecimal) dailyData.getOrDefault("totalGeneration", BigDecimal.ZERO);
                    detailData = (List<StationDetailResponseDTO.PowerGenerationDetailData>)
                            dailyData.getOrDefault("detailData", new ArrayList<>());
                    break;
                default:
                    log.warn("不支持的维度: {}", dimension);
            }

            powerGeneration.setCurrentPeriodGeneration(currentPeriodGeneration);
            powerGeneration.setDetailData(detailData);

            return powerGeneration;

        } catch (Exception e) {
            log.error("构建功率预测发电量信息失败 - 电站ID: {}", stationId, e);
            return stationDetailHelper.buildDefaultPowerGenerationInfo(dimension, date);
        }
    }

    /**
     * 获取日维度功率预测数据并计算发电量 - 确保返回96个数据点
     */
    private Map<String, Object> getPowerForecastDailyData(Long stationId, String date) {
        Map<String, Object> result = new HashMap<>();
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();
        BigDecimal totalGeneration = BigDecimal.ZERO;

        try {
            // 查询功率预测实际功率数据
            List<RpJiaYue> powerData = queryRealPowerData(stationId, date, date);

            // 创建时间->数据的映射
            Map<String, RpJiaYue> dataMap = new HashMap<>();
            if (powerData != null && !powerData.isEmpty()) {
                for (RpJiaYue rp : powerData) {
                    if (rp.getTime() != null) {
                        dataMap.put(rp.getTime(), rp);
                    }
                }
            }

            // 生成完整的96个时间点（15分钟间隔）
            List<String> timePoints = generateDailyTimePoints();
            log.info("生成的时间点数量: {}, 示例时间点: {}", timePoints.size(),
                    timePoints.size() > 0 ? timePoints.subList(0, Math.min(5, timePoints.size())) : "无");

            for (String timePoint : timePoints) {
                StationDetailResponseDTO.PowerGenerationDetailData detail =
                        new StationDetailResponseDTO.PowerGenerationDetailData();

                // 时间标签
                detail.setTimeLabel(timePoint);

                // 获取实际数据或使用0值
                RpJiaYue rp = dataMap.get(timePoint);
                Double actualPower = 0.0;
                if (rp != null && rp.getValue() != null) {
                    actualPower = rp.getValue();
                }
                detail.setActualPower(actualPower);

                // 发电量计算：与PowerService.java保持一致，实际功率/4
                Double generation = actualPower / 4.0;
                detail.setGeneration(generation);

                detailData.add(detail);

                // 累计发电量
                totalGeneration = totalGeneration.add(BigDecimal.valueOf(generation));
            }

            result.put("detailData", detailData);
            result.put("totalGeneration", totalGeneration);

            log.info("电站{}日维度功率预测数据查询完成 - 数据点数: {}, 总发电量: {} MWh, 实际数据点: {}",
                    stationId, detailData.size(), totalGeneration, dataMap.size());

        } catch (Exception e) {
            log.error("获取电站{}日维度功率预测数据失败", stationId, e);
            // 即使出错也要返回96个0值数据点
            detailData = generateZeroDailyData();
            result.put("detailData", detailData);
            result.put("totalGeneration", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 获取月维度功率预测数据 - 返回该月每一天的数据，确保返回完整天数
     */
    private Map<String, Object> getPowerForecastMonthlyData(Long stationId, String date) {
        Map<String, Object> result = new HashMap<>();
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();
        BigDecimal totalGeneration = BigDecimal.ZERO;

        try {
            // 解析年月
            String year = date.length() >= 4 ? date.substring(0, 4) : "2024";
            String month = date.length() >= 7 ? date.substring(5, 7) : "01";

            // 计算该月的天数
            int daysInMonth = getDaysInMonth(year, month);

            // 遍历该月的每一天，确保返回完整天数的数据
            for (int day = 1; day <= daysInMonth; day++) {
                String dayStr = String.format("%02d", day);
                String currentDate = year + "-" + month + "-" + dayStr;

                // 查询当天的功率预测数据
                List<RpJiaYue> dailyPowerData = queryRealPowerData(stationId, currentDate, currentDate);

                // 计算当天的总发电量：与PowerService.java保持一致，累加所有功率值/4
                double dailyGeneration = 0.0;
                if (dailyPowerData != null && !dailyPowerData.isEmpty()) {
                    dailyGeneration = dailyPowerData.stream()
                            .filter(rp -> rp.getValue() != null)
                            .mapToDouble(rp -> rp.getValue() / 4.0)
                            .sum();
                }

                // 构建当天的数据 - 即使没有数据也要返回0值
                StationDetailResponseDTO.PowerGenerationDetailData dayDetail =
                        new StationDetailResponseDTO.PowerGenerationDetailData();
                dayDetail.setTimeLabel(month + "-" + dayStr);  // 时间标签：如"01-15"
                dayDetail.setActualPower(0.0);  // 月维度显示0值而非null
                dayDetail.setGeneration(dailyGeneration);  // 当天总发电量，可能为0

                detailData.add(dayDetail);
                totalGeneration = totalGeneration.add(BigDecimal.valueOf(dailyGeneration));
            }

            result.put("detailData", detailData);
            result.put("totalGeneration", totalGeneration);

            log.info("电站{}月维度功率预测数据查询完成 - 天数: {}, 总发电量: {} MWh, 实际有数据天数: {}",
                    stationId, daysInMonth, totalGeneration,
                    detailData.stream().mapToDouble(d -> d.getGeneration()).filter(g -> g > 0).count());

        } catch (Exception e) {
            log.error("获取电站{}月维度功率预测数据失败", stationId, e);
            // 即使出错也要返回完整月份的0值数据
            String year = date.length() >= 4 ? date.substring(0, 4) : "2024";
            String month = date.length() >= 7 ? date.substring(5, 7) : "01";
            int daysInMonth = getDaysInMonth(year, month);
            detailData = generateZeroMonthlyData(month, daysInMonth);
            result.put("detailData", detailData);
            result.put("totalGeneration", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 获取年维度功率预测数据 - 返回该年每一个月的数据
     */
    private Map<String, Object> getPowerForecastYearlyData(Long stationId, String date) {
        Map<String, Object> result = new HashMap<>();
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();
        BigDecimal totalGeneration = BigDecimal.ZERO;

        try {
            // 解析年份
            String year = date.length() >= 4 ? date.substring(0, 4) : "2024";

            // 遍历该年的每一个月
            log.info("开始处理年维度数据 - 电站ID: {}, 年份: {}", stationId, year);
            for (int month = 1; month <= 12; month++) {
                String monthStr = String.format("%02d", month);

                // 计算该月的天数
                int daysInMonth = getDaysInMonth(year, monthStr);

                // 构建该月的开始和结束日期
                String startDate = year + "-" + monthStr + "-01";
                String endDate = year + "-" + monthStr + "-" + String.format("%02d", daysInMonth);

                // 查询该月的功率预测数据
                List<RpJiaYue> monthlyPowerData = queryRealPowerData(stationId, startDate, endDate);

                // 计算该月的总发电量：与PowerService.java保持一致，累加所有功率值/4
                double monthlyGeneration = 0.0;
                if (monthlyPowerData != null && !monthlyPowerData.isEmpty()) {
                    monthlyGeneration = monthlyPowerData.stream()
                            .filter(rp -> rp.getValue() != null)
                            .mapToDouble(rp -> rp.getValue() / 4.0)
                            .sum();
                }

                // 构建该月的数据 - 即使没有数据也要返回0值
                StationDetailResponseDTO.PowerGenerationDetailData monthDetail =
                        new StationDetailResponseDTO.PowerGenerationDetailData();
                monthDetail.setTimeLabel(year + "-" + monthStr);  // 时间标签：如"2025-01"
                monthDetail.setActualPower(0.0);  // 年维度显示0值而非null
                monthDetail.setGeneration(monthlyGeneration);  // 该月总发电量，可能为0

                detailData.add(monthDetail);
                totalGeneration = totalGeneration.add(BigDecimal.valueOf(monthlyGeneration));
            }

            result.put("detailData", detailData);
            result.put("totalGeneration", totalGeneration);

            log.info("电站{}年维度功率预测数据查询完成 - 总发电量: {} MWh, 实际有数据月数: {}",
                    stationId, totalGeneration,
                    detailData.stream().mapToDouble(d -> d.getGeneration()).filter(g -> g > 0).count());

        } catch (Exception e) {
            log.error("获取电站{}年维度功率预测数据失败", stationId, e);
            // 即使出错也要返回12个月的0值数据
            String year = date.length() >= 4 ? date.substring(0, 4) : "2024";
            detailData = generateZeroYearlyData(year);
            result.put("detailData", detailData);
            result.put("totalGeneration", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 查询实际功率数据 - 使用与发电趋势接口相同的数据源
     */
    private List<RpJiaYue> queryRealPowerData(Long stationId, String startDate, String endDate) {
        try {
            log.info("开始查询电站{}实际功率数据 - 时间范围: {} 到 {}", stationId, startDate, endDate);

            // 使用PowerService统一的查询策略，与发电趋势接口保持一致
            PowerQueryParam powerQueryParam = new PowerQueryParam();
            powerQueryParam.setStationId(stationId);
            powerQueryParam.setStartDate(startDate);
            powerQueryParam.setEndDate(endDate);

            List<Long> stationIds = new ArrayList<>();
            stationIds.add(stationId);
            powerQueryParam.setStationIds(stationIds);

            // 调用PowerService获取实际功率数据
            Map<Long, Map<Integer, List<CommonPowerDto>>> powerData = powerService.queryPowerRealByRange(powerQueryParam);

            List<RpJiaYue> result = new ArrayList<>();

            if (powerData != null && !powerData.isEmpty()) {
                Map<Integer, List<CommonPowerDto>> stationPowerData = powerData.get(stationId);
                if (stationPowerData != null && !stationPowerData.isEmpty()) {
                    // 遍历所有厂家的数据
                    for (Map.Entry<Integer, List<CommonPowerDto>> entry : stationPowerData.entrySet()) {
                        List<CommonPowerDto> powerDtoList = entry.getValue();
                        if (powerDtoList != null && !powerDtoList.isEmpty()) {
                            // 转换CommonPowerDto为RpJiaYue格式
                            for (CommonPowerDto dto : powerDtoList) {
                                RpJiaYue rp = new RpJiaYue();
                                rp.setDate(dto.getDate());
                                rp.setTime(dto.getTime());
                                rp.setValue(dto.getValue());
                                rp.setStationId(String.valueOf(stationId));
                                rp.setType(1); // 瞬时值
                                result.add(rp);
                            }
                            break; // 找到数据就退出，避免重复
                        }
                    }
                }
            }

            if (result.isEmpty()) {
                log.warn("未查询到电站{}的功率数据 - 时间范围: {} 到 {}", stationId, startDate, endDate);

                // 尝试查询更大的时间范围
                String expandedStartDate = startDate.substring(0, 7) + "-01"; // 月初
                powerQueryParam.setStartDate(expandedStartDate);

                Map<Long, Map<Integer, List<CommonPowerDto>>> expandedData = powerService.queryPowerRealByRange(powerQueryParam);

                if (expandedData != null && !expandedData.isEmpty()) {
                    Map<Integer, List<CommonPowerDto>> expandedStationData = expandedData.get(stationId);
                    if (expandedStationData != null && !expandedStationData.isEmpty()) {
                        for (Map.Entry<Integer, List<CommonPowerDto>> entry : expandedStationData.entrySet()) {
                            List<CommonPowerDto> powerDtoList = entry.getValue();
                            if (powerDtoList != null && !powerDtoList.isEmpty()) {
                                for (CommonPowerDto dto : powerDtoList) {
                                    RpJiaYue rp = new RpJiaYue();
                                    rp.setDate(dto.getDate());
                                    rp.setTime(dto.getTime());
                                    rp.setValue(dto.getValue());
                                    rp.setStationId(String.valueOf(stationId));
                                    rp.setType(1);
                                    result.add(rp);
                                }
                                break;
                            }
                        }

                        if (!result.isEmpty()) {
                            log.info("在扩展时间范围内查询到电站{}的功率数据 - 数据点数: {}", stationId, result.size());
                            return result;
                        }
                    }
                }

                return new ArrayList<>();
            }

            log.info("查询电站{}实际功率数据成功 - 时间范围: {} 到 {}, 数据点数: {}, 示例数据: {}",
                    stationId, startDate, endDate, result.size(),
                    result.size() > 0 ? result.get(0) : "无");

            return result;

        } catch (Exception e) {
            log.error("查询电站{}实际功率数据失败 - 时间范围: {} 到 {}", stationId, startDate, endDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取月份的天数
     */
    private int getDaysInMonth(String year, String month) {
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 使用Java 8的LocalDate计算月份天数
            java.time.LocalDate date = java.time.LocalDate.of(yearInt, monthInt, 1);
            return date.lengthOfMonth();

        } catch (Exception e) {
            log.warn("计算月份天数失败，使用默认值31 - 年: {}, 月: {}", year, month, e);
            return 31; // 默认返回31
        }
    }

    /**
     * 构建单个电站的交易概况数据
     */
    private Map<String, Object> buildStationTradingOverview(Station station, Integer dimension, String year, String month) {
        Map<String, Object> result = new HashMap<>();

        result.put("station", station);
        try {
            if (dimension == 1 && Objects.nonNull(month)) {
                // 月度查询：返回指定月份的数据
                Map<String, Object> monthlyData = buildMonthlyTradingData(station, year, month);
                result.putAll(monthlyData);
                log.debug("构建月度查询数据完成 - 电站ID: {}, 年份: {}, 月份: {}", station.getId(), year, month);

            } else if (dimension == 2) {
                // 年度查询：返回该年每个月的数据列表
                List<Map<String, Object>> monthlyDataList = buildYearlyTradingDataList(station, year);
                result.put("monthlyDataList", monthlyDataList);

                // 计算年度汇总数据
                Map<String, Object> yearlyTotal = calculateYearlyTotal(monthlyDataList);
                result.putAll(yearlyTotal);

                log.debug("构建年度查询数据完成 - 电站ID: {}, 年份: {}, 月份数: {}", station.getId(), year, monthlyDataList.size());
            }

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            // 异常时设置正确的默认值
            result.put("totalElectricity", BigDecimal.ZERO);
            result.put("averagePrice", BigDecimal.ZERO);
            result.put("current_month_power", BigDecimal.ZERO);
            if (dimension == 2) {
                result.put("monthlyDataList", new ArrayList<>());
            }
        }

        return result;
    }

    /**
     * 构建单月交易数据
     */
    private Map<String, Object> buildMonthlyTradingData(Station station, String year, String month) {
        Map<String, Object> monthData = new HashMap<>();

        LambdaQueryWrapper<ScreenTradeSettlement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                   .eq(ScreenTradeSettlement::getYear, year)
                   .eq(ScreenTradeSettlement::getMonth, month);

        List<ScreenTradeSettlement> settlementList = screenTradeSettlementService.list(queryWrapper);

        if (settlementList != null && !settlementList.isEmpty()) {
            // 计算月度汇总数据
            BigDecimal totalElectricity = settlementList.stream()
                    .map(ScreenTradeSettlement::getSettlePower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalFee = settlementList.stream()
                    .map(ScreenTradeSettlement::getSettleElectricityFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal current_month_power = settlementList.stream()
                    .map(ScreenTradeSettlement::getCurrentMonthPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal current_month_plan_power = settlementList.stream()
                    .map(ScreenTradeSettlement::getCurrentMonthPlanPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 交易均价 = 总电费 ÷ 总电量
            BigDecimal averagePrice = totalElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                    totalFee.divide(totalElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

            monthData.put("year", year);
            monthData.put("month", month);
            monthData.put("totalElectricity", current_month_plan_power);
            monthData.put("current_month_power", current_month_power);
            monthData.put("averagePrice", averagePrice);
            monthData.put("settlementList", settlementList);

        } else {
            // 无数据时设置默认值
            monthData.put("year", year);
            monthData.put("month", month);
            monthData.put("totalElectricity", BigDecimal.ZERO);
            monthData.put("current_month_power", BigDecimal.ZERO);
            monthData.put("averagePrice", BigDecimal.ZERO);
            monthData.put("settlementList", new ArrayList<>());
        }

        return monthData;
    }

    /**
     * 构建年度每月交易数据列表
     */
    private List<Map<String, Object>> buildYearlyTradingDataList(Station station, String year) {
        List<Map<String, Object>> monthlyDataList = new ArrayList<>();

        // 查询该年所有月份的数据
        LambdaQueryWrapper<ScreenTradeSettlement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                   .eq(ScreenTradeSettlement::getYear, year)
                   .orderByAsc(ScreenTradeSettlement::getMonth);

        List<ScreenTradeSettlement> allSettlements = screenTradeSettlementService.list(queryWrapper);

        // 按月份分组
        Map<String, List<ScreenTradeSettlement>> monthlyGrouped = allSettlements.stream()
                .collect(Collectors.groupingBy(ScreenTradeSettlement::getMonth));

        // 为每个月生成数据（1-12月）
        for (int monthNum = 1; monthNum <= 12; monthNum++) {
            String monthStr = String.format("%02d", monthNum);
            List<ScreenTradeSettlement> monthSettlements = monthlyGrouped.getOrDefault(monthStr, new ArrayList<>());

            Map<String, Object> monthData = new HashMap<>();
            monthData.put("year", year);
            monthData.put("month", monthStr);

            if (!monthSettlements.isEmpty()) {
                // 计算该月汇总数据
                BigDecimal totalElectricity = monthSettlements.stream()
                        .map(ScreenTradeSettlement::getSettlePower)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalFee = monthSettlements.stream()
                        .map(ScreenTradeSettlement::getSettleElectricityFee)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal current_month_power = monthSettlements.stream()
                        .map(ScreenTradeSettlement::getCurrentMonthPower)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal current_month_plan_power = monthSettlements.stream()
                        .map(ScreenTradeSettlement::getCurrentMonthPlanPower)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal averagePrice = totalElectricity.compareTo(BigDecimal.ZERO) > 0 ?
                        totalFee.divide(totalElectricity, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

                monthData.put("totalElectricity", current_month_plan_power);
                monthData.put("current_month_power", current_month_power);
                monthData.put("averagePrice", averagePrice);
                monthData.put("recordCount", monthSettlements.size());

            } else {
                // 该月无数据，设置为0
                monthData.put("totalElectricity", BigDecimal.ZERO);
                monthData.put("current_month_power", BigDecimal.ZERO);
                monthData.put("averagePrice", BigDecimal.ZERO);
                monthData.put("recordCount", 0);
            }

            monthlyDataList.add(monthData);
        }

        return monthlyDataList;
    }

    /**
     * 计算年度汇总数据
     */
    private Map<String, Object> calculateYearlyTotal(List<Map<String, Object>> monthlyDataList) {
        Map<String, Object> yearlyTotal = new HashMap<>();

        BigDecimal totalElectricity = monthlyDataList.stream()
                .map(data -> (BigDecimal) data.get("totalElectricity"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCurrentMonthPower = monthlyDataList.stream()
                .map(data -> (BigDecimal) data.get("current_month_power"))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 年度平均电价：所有月份的加权平均
        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalPower = BigDecimal.ZERO;

        for (Map<String, Object> monthData : monthlyDataList) {
            BigDecimal monthElectricity = (BigDecimal) monthData.get("totalElectricity");
            BigDecimal monthPrice = (BigDecimal) monthData.get("averagePrice");

            if (monthElectricity != null && monthPrice != null &&
                monthElectricity.compareTo(BigDecimal.ZERO) > 0) {
                totalFee = totalFee.add(monthElectricity.multiply(monthPrice));
                totalPower = totalPower.add(monthElectricity);
            }
        }

        BigDecimal yearlyAveragePrice = totalPower.compareTo(BigDecimal.ZERO) > 0 ?
                totalFee.divide(totalPower, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        yearlyTotal.put("totalElectricity", totalElectricity);
        yearlyTotal.put("current_month_power", totalCurrentMonthPower);
        yearlyTotal.put("averagePrice", yearlyAveragePrice);

        return yearlyTotal;
    }

    @Override
    public List<EnergyStorageDailyClean> getSingleProvinceEnergyStorageDailyClean(String provinceId, String stationId,
                                                                                  String date, String dimension) {
        log.info("开始获取单省份储能日清洁数据 - 省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}",
                provinceId, stationId, date, dimension);

        try {
            // 获取数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(Integer.parseInt(provinceId));
            if (dsKey == null) {
                log.warn("不支持的省份ID: {}", provinceId);
                return new ArrayList<>();
            }

            DynamicDataSourceContextHolder.push(dsKey);
            try {
                // 根据维度调用不同的查询方法
                List<EnergyStorageDailyClean> result;
                switch (dimension.toLowerCase()) {
                    case "day":
                        result = energyStorageDailyCleanService.selectByDay(stationId, date);
                        break;
                    case "month":
                        result = energyStorageDailyCleanService.selectByMonth(Long.parseLong(stationId), date);
                        break;
                    case "year":
                        result = energyStorageDailyCleanService.selectByYear(stationId, date);
                        break;
                    default:
                        log.warn("不支持的查询维度: {}", dimension);
                        result = new ArrayList<>();
                }

                log.info("单省份储能数据查询完成 - 省份ID: {}, 记录数: {}", provinceId, result.size());
                return result;

            } finally {
                DynamicDataSourceContextHolder.clear();
            }

        } catch (Exception e) {
            log.error("获取单省份储能日清洁数据失败 - 省份ID: {}, 电站ID: {}", provinceId, stationId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 生成日维度的96个时间点（15分钟间隔）
     */
    private List<String> generateDailyTimePoints() {
        List<String> timePoints = new ArrayList<>();
        for (int hour = 0; hour < 24; hour++) {
            for (int minute = 0; minute < 60; minute += 15) {
                String timePoint = String.format("%02d:%02d", hour, minute);
                timePoints.add(timePoint);
            }
        }
        return timePoints;
    }

    /**
     * 生成日维度的96个0值数据点
     */
    private List<StationDetailResponseDTO.PowerGenerationDetailData> generateZeroDailyData() {
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();
        List<String> timePoints = generateDailyTimePoints();

        for (String timePoint : timePoints) {
            StationDetailResponseDTO.PowerGenerationDetailData detail =
                    new StationDetailResponseDTO.PowerGenerationDetailData();
            detail.setTimeLabel(timePoint);
            detail.setActualPower(0.0);
            detail.setGeneration(0.0);
            detailData.add(detail);
        }

        return detailData;
    }

    /**
     * 生成月维度的0值数据（该月的每一天）
     */
    private List<StationDetailResponseDTO.PowerGenerationDetailData> generateZeroMonthlyData(String month, int daysInMonth) {
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();

        for (int day = 1; day <= daysInMonth; day++) {
            String dayStr = String.format("%02d", day);
            StationDetailResponseDTO.PowerGenerationDetailData detail =
                    new StationDetailResponseDTO.PowerGenerationDetailData();
            detail.setTimeLabel(month + "-" + dayStr);
            detail.setActualPower(0.0);
            detail.setGeneration(0.0);
            detailData.add(detail);
        }

        return detailData;
    }

    /**
     * 生成年维度的12个月0值数据
     */
    private List<StationDetailResponseDTO.PowerGenerationDetailData> generateZeroYearlyData(String year) {
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();

        for (int month = 1; month <= 12; month++) {
            String monthStr = String.format("%02d", month);
            StationDetailResponseDTO.PowerGenerationDetailData detail =
                    new StationDetailResponseDTO.PowerGenerationDetailData();
            detail.setTimeLabel(year + "-" + monthStr);
            detail.setActualPower(0.0);
            detail.setGeneration(0.0);
            detailData.add(detail);
        }

        return detailData;
    }
}
