package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@ApiModel("首页概览数据DTO")
@Data
public class DashboardSummaryDTO {
    @ApiModelProperty(value = "场站类型统计信息", notes = "包含各类场站的统计信息")
    private StationTypeStatisticsDTO stationTypeStatistics;

    @ApiModelProperty(value = "能源类型数量统计", notes = "按能源类型分类的场站数量统计")
    private Map<Integer, Integer> energyTypeCount;
}

