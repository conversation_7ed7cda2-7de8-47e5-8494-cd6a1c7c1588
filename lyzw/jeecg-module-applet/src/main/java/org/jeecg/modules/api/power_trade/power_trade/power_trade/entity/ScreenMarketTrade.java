package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("screen_market_trade")
@ApiModel(value = "ScreenMarketTrade", description = "市场交易情况")
public class ScreenMarketTrade {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("type")
    @ApiModelProperty(value = "数据类型", example = "现货交易")
    private String type;

    @TableField("year")
    @ApiModelProperty(value = "年份", example = "2024")
    private String year;

    @TableField("month")
    @ApiModelProperty(value = "月份", example = "01")
    private String month;

    @TableField("power")
    @ApiModelProperty(value = "成交电量", example = "1000.50")
    private BigDecimal power;

    @TableField("price")
    @ApiModelProperty(value = "成交均价", example = "0.45")
    private BigDecimal price;

    @TableField("discharge_price")
    @ApiModelProperty(value = "放电均价", example = "0.40")
    private BigDecimal dischargePrice;
}