package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.TradeDiary;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;
import org.jeecg.modules.api.power_trade.mapper.TradeDiaryMapper;
import org.jeecg.modules.api.power_trade.service.ITradeDiaryFileService;
import org.jeecg.modules.api.power_trade.service.ITradeDiaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易日历服务实现
 */
@Service
public class TradeDiaryServiceImpl extends ServiceImpl<TradeDiaryMapper, TradeDiary> implements ITradeDiaryService {

    @Resource
    private ITradeDiaryFileService tradeDiaryFileService;

    @Override
    public List<TradeDiary> getTradeDiaryByDateRange(Date startDate, Date endDate, List<Integer> provinceIds) {
        // 已经通过数据源切换到特定省份，不再需要provinceIds参数
        List<TradeDiary> diaries = baseMapper.selectTradeDiaryByDateRange(startDate, endDate, null);
        loadDiaryFiles(diaries);
        return diaries;
    }
    
    @Override
    public Map<String, Object> getTradeCalendarByProvinceWithPage(Integer provinceId, int pageNo, int pageSize) {
        // 计算偏移量
        int offset = (pageNo - 1) * pageSize;
        
        // 查询总记录数
        int total = baseMapper.countTradeCalendarByProvince(provinceId);
        
        // 查询当前页数据
        List<Map<String, Object>> records = baseMapper.getTradeCalendarByProvinceWithPage(provinceId, pageSize, offset);
        
        // 合并相同日记ID的记录，将附件归类到对应日记下
        Map<Long, Map<String, Object>> diaryMap = new HashMap<>();
        
        for (Map<String, Object> record : records) {
            Long diaryId = (Long) record.get("id");
            Long fileId = (Long) record.get("file_id");
            
            if (!diaryMap.containsKey(diaryId)) {
                // 创建新的日记记录
                Map<String, Object> diary = new HashMap<>();
                diary.put("id", diaryId);
                diary.put("target_date", record.get("target_date"));
                diary.put("day_ahead_record", record.get("day_ahead_record"));
                diary.put("rolling_record", record.get("rolling_record"));
                diary.put("create_time", record.get("create_time"));
                diary.put("update_time", record.get("update_time"));
                diary.put("station_name", record.get("station_name"));
                diary.put("station_id", record.get("station_id"));
                diary.put("province_id", record.get("province_id"));
                diary.put("province_name", record.get("province_name"));
                diary.put("files", new ArrayList<Map<String, Object>>());
                
                diaryMap.put(diaryId, diary);
            }
            
            // 如果有附件，则添加到日记的附件列表中
            if (fileId != null) {
                Map<String, Object> file = new HashMap<>();
                file.put("id", fileId);
                file.put("trade_diary_id", record.get("trade_diary_id"));
                file.put("type", record.get("file_type"));
                file.put("file_name", record.get("file_name"));
                file.put("file_url", record.get("file_url"));
                
                ((List<Map<String, Object>>) diaryMap.get(diaryId).get("files")).add(file);
            }
        }
        
        // 构造返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("records", new ArrayList<>(diaryMap.values()));
        result.put("total", total);
        result.put("pages", (total + pageSize - 1) / pageSize);  // 计算总页数
        result.put("current", pageNo);
        result.put("size", pageSize);
        
        return result;
    }
    
    /**
     * 加载交易日历关联的附件
     * @param diaries 交易日历列表
     */
    private void loadDiaryFiles(List<TradeDiary> diaries) {
        if (diaries == null || diaries.isEmpty()) {
            return;
        }
        
        // 提取所有日历ID
        List<Long> diaryIds = diaries.stream().map(TradeDiary::getId).collect(Collectors.toList());
        
        // 批量查询所有附件并按日历ID分组
        Map<Long, List<TradeDiaryFile>> fileMap = tradeDiaryFileService.getFilesByDiaryIds(diaryIds);
        
        // 将附件设置到对应的交易日历中
        diaries.forEach(diary -> diary.setFiles(fileMap.getOrDefault(diary.getId(), new ArrayList<>())));
    }
} 