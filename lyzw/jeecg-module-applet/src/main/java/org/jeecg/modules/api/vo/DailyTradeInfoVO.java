package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 日交易信息响应VO
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Data
@ApiModel(value = "DailyTradeInfoVO", description = "日交易信息响应对象")
public class DailyTradeInfoVO {

    @ApiModelProperty(value = "日期", example = "2024-07-01")
    private String date;

    @ApiModelProperty(value = "省份ID", example = "2")
    private Integer provinceId;

    @ApiModelProperty(value = "省份名称", example = "安徽")
    private String provinceName;

    @ApiModelProperty(value = "电站类型", example = "photovoltaic")
    private String stationType;

    @ApiModelProperty(value = "电站ID", example = "1001")
    private Long stationId;

    // ==================== 安徽省光伏/风电字段 (energy_new_daily_clean) ====================
    
    @ApiModelProperty(value = "中长期合约电量(万千瓦时)", example = "100.12345")
    private BigDecimal midLongTermPower;

    @ApiModelProperty(value = "中长期单价(元/千瓦时)", example = "0.5")
    private BigDecimal midLongTermPrice;

    @ApiModelProperty(value = "中长期电费(万元)", example = "50.06173")
    private BigDecimal midLongTermFee;

    @ApiModelProperty(value = "保障电量(万千瓦时)", example = "80.12345")
    private BigDecimal guaranteePower;

    @ApiModelProperty(value = "保障电价(元/千瓦时)", example = "0.45")
    private BigDecimal guaranteePrice;

    @ApiModelProperty(value = "保障电费(万元)", example = "36.05555")
    private BigDecimal guaranteeFee;

    @ApiModelProperty(value = "日前偏差电量(万千瓦时)", example = "10.12345")
    private BigDecimal dayAheadDeviationPower;

    @ApiModelProperty(value = "日前偏差单价(元/千瓦时)", example = "0.48")
    private BigDecimal dayAheadDeviationPrice;

    @ApiModelProperty(value = "日前偏差电费(万元)", example = "4.85926")
    private BigDecimal dayAheadDeviationFee;

    @ApiModelProperty(value = "实时偏差电量(万千瓦时)", example = "5.12345")
    private BigDecimal realtimeDeviationPower;

    @ApiModelProperty(value = "实时偏差单价(元/千瓦时)", example = "0.52")
    private BigDecimal realtimeDeviationPrice;

    @ApiModelProperty(value = "实时偏差电费(万元)", example = "2.66419")
    private BigDecimal realtimeDeviationFee;

    @ApiModelProperty(value = "超额收益回收(万元)", example = "1.23456")
    private BigDecimal excessProfitRecovery;

    @ApiModelProperty(value = "日前收益回收(万元)", example = "0.98765")
    private BigDecimal dayAheadProfitRecovery;

    @ApiModelProperty(value = "总电量(万千瓦时)", example = "195.37035")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "总电费(万元)", example = "93.58519")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "结算均价(元/千瓦时)", example = "0.479")
    private BigDecimal settlementAvgPrice;

    // ==================== 储能用户侧字段 (energy_storage_daily_clean) ====================
    
    @ApiModelProperty(value = "用户侧日前偏差电量(万千瓦时)", example = "10.12345")
    private BigDecimal userDayAheadDeviationPower;

    @ApiModelProperty(value = "用户侧日前平均电价(元/千瓦时)", example = "0.5")
    private BigDecimal userDayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "用户侧日前电费(万元)", example = "5.06173")
    private BigDecimal userDayAheadDeviationFee;

    @ApiModelProperty(value = "用户侧实时偏差电量(万千瓦时)", example = "8.12345")
    private BigDecimal userRealtimeDeviationPower;

    @ApiModelProperty(value = "用户侧实时平均单价(元/千瓦时)", example = "0.48")
    private BigDecimal userRealtimeDeviationAveragePrice;

    @ApiModelProperty(value = "用户侧实时电费(万元)", example = "3.89926")
    private BigDecimal userRealtimeDeviationFee;

    @ApiModelProperty(value = "用户侧用电总电量(万千瓦时)", example = "18.2469")
    private BigDecimal userTotalPower;

    @ApiModelProperty(value = "用户侧用电总费用(万元)", example = "8.96099")
    private BigDecimal userTotalFee;

    // ==================== 储能发电侧字段 (energy_storage_daily_clean) ====================
    
    @ApiModelProperty(value = "发电侧日前偏差电量(万千瓦时)", example = "12.12345")
    private BigDecimal powerGenerationDayAheadDeviationPower;

    @ApiModelProperty(value = "发电侧日前平均电价(元/千瓦时)", example = "0.52")
    private BigDecimal powerGenerationDayAheadDeviationAveragePrice;

    @ApiModelProperty(value = "发电侧日前电费(万元)", example = "6.30419")
    private BigDecimal powerGenerationDayAheadDeviationFee;

    @ApiModelProperty(value = "发电侧实时偏差电量(万千瓦时)", example = "9.12345")
    private BigDecimal powerGenerationRealtimeDeviationPower;

    @ApiModelProperty(value = "发电侧实时平均单价(元/千瓦时)", example = "0.49")
    private BigDecimal powerGenerationRealtimeDeviationAveragePrice;

    @ApiModelProperty(value = "发电侧实时电费(万元)", example = "4.47049")
    private BigDecimal powerGenerationRealtimeDeviationFee;

    @ApiModelProperty(value = "发电侧发电总电量(万千瓦时)", example = "21.2469")
    private BigDecimal powerGenerationTotalPower;

    @ApiModelProperty(value = "发电侧发电总费用(万元)", example = "10.77468")
    private BigDecimal powerGenerationTotalFee;
}
