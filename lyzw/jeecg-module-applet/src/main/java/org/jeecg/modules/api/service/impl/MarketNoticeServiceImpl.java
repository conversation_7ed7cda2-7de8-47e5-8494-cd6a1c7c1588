package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.entity.MarketNotice;
import org.jeecg.modules.api.power_trade.entity.NoticeFile;
import org.jeecg.modules.api.power_trade.entity.NoticeType;
import org.jeecg.modules.api.power_trade.mapper.MarketNoticeMapper;
import org.jeecg.modules.api.power_trade.mapper.NoticeFileMapper;
import org.jeecg.modules.api.power_trade.mapper.NoticeTypeMapper;
import org.jeecg.modules.api.power_trade.service.MarketNoticeService;
import org.jeecg.modules.api.power_trade.vo.MarketNoticeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MarketNoticeServiceImpl extends ServiceImpl<MarketNoticeMapper, MarketNotice> implements MarketNoticeService {

    @Value("${jeecg.minio.externalEndpoint}")
    private String externalEndpoint;

    @Resource
    private MarketNoticeMapper marketNoticeMapper;

    @Resource
    private NoticeFileMapper noticeFileMapper;

    @Resource
    private NoticeTypeMapper noticeTypeMapper;

    @Override
    public IPage<MarketNoticeVO> getAnnouncementList(Page<MarketNotice> page, Integer typeId, Integer provinceId, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<MarketNotice> queryWrapper = new LambdaQueryWrapper<>();

        // 根据类型ID筛选
        queryWrapper.eq(typeId != null, MarketNotice::getTypeId, typeId);

        // 根据关键词筛选标题
        queryWrapper.like(StringUtils.isNotBlank(keyword), MarketNotice::getNoticeTitle, keyword);

        // 根据通知日期降序排序
        queryWrapper.orderByDesc(MarketNotice::getNoticeDate);

        // 查询分页数据
        Page<MarketNotice> noticePage = marketNoticeMapper.selectPage(page, queryWrapper);

        // 查询前初始化返回结果
        Page<MarketNoticeVO> resultPage = new Page<>();
        BeanUtils.copyProperties(noticePage, resultPage, "records");

        // 记录集合为空则直接返回
        if (CollectionUtils.isEmpty(noticePage.getRecords())) {
            return resultPage;
        }

        // 获取所有公告类型，构建ID到类型名称的映射
        Map<Integer, String> typeMap = this.getAllNoticeTypesList().stream()
                .collect(Collectors.toMap(NoticeType::getId, NoticeType::getTypeName, (k1, k2) -> k1));

        // 获取所有通知ID
        List<Long> noticeIds = noticePage.getRecords().stream()
                .map(MarketNotice::getId)
                .collect(Collectors.toList());

        // 批量查询所有相关附件
        Map<Long, List<NoticeFile>> fileMap;
        if (!noticeIds.isEmpty()) {
            List<NoticeFile> allFiles = noticeFileMapper.selectList(
                    Wrappers.<NoticeFile>lambdaQuery()
                            .in(NoticeFile::getNoticeId, noticeIds)
            );

            // 按通知ID分组
            fileMap = allFiles.stream()
                    .collect(Collectors.groupingBy(NoticeFile::getNoticeId));
        } else {
            fileMap = new HashMap<>();
        }

        // 转换为VO列表
        List<MarketNoticeVO> records = noticePage.getRecords().stream().map(notice -> {
            MarketNoticeVO vo = new MarketNoticeVO();
            BeanUtils.copyProperties(notice, vo);

            // 设置类型名称
            vo.setTypeName(typeMap.getOrDefault(notice.getTypeId(), ""));

            // 设置附件信息
            List<NoticeFile> files = fileMap.getOrDefault(notice.getId(), Collections.emptyList());
            vo.setAttachments(files);
            vo.setAttachmentCount(files.size());

            if (!files.isEmpty() && files.get(0).getFileUrl() != null) {
                vo.setFileExistUrl(externalEndpoint + files.get(0).getFileUrl());
            } else {
                vo.setFileExistUrl(null);
            }
            String provinceName = ProvinceDataSourceUtil.getProvinceName(provinceId);
            List<String> tags = new ArrayList<>();
            tags.add(provinceName);
            vo.setTags(tags);

            // 截取内容前100字符作为摘要
            if (StringUtils.isNotBlank(notice.getNoticeContent())) {
                String content = notice.getNoticeContent().replaceAll("<[^>]+>", "");
                vo.setSummary(StringUtils.substring(content, 0, 100));
            }

            return vo;
        }).collect(Collectors.toList());

        resultPage.setRecords(records);
        return resultPage;
    }

    @Override
    public MarketNoticeVO getAnnouncementDetail(Long id) {
        MarketNotice notice = marketNoticeMapper.selectById(id);
        if (notice == null) {
            return null;
        }

        MarketNoticeVO vo = new MarketNoticeVO();
        BeanUtils.copyProperties(notice, vo);

        // 设置类型名称
        NoticeType noticeType = noticeTypeMapper.selectById(notice.getTypeId());
        if (noticeType != null) {
            vo.setTypeName(noticeType.getTypeName());
        }

        // 查询附件列表
        List<NoticeFile> files = noticeFileMapper.selectList(
                Wrappers.<NoticeFile>lambdaQuery()
                        .eq(NoticeFile::getNoticeId, id)
        );

        vo.setAttachments(files);
        // 设置附件数量
        vo.setAttachmentCount(files != null ? files.size() : 0);

        assert files != null;
        if (!files.isEmpty() && files.get(0).getFileUrl() != null) {
            vo.setFileExistUrl( externalEndpoint + files.get(0).getFileUrl());
        } else {
            vo.setFileExistUrl(null);
        }

        return vo;
    }

    @Override
    public List<MarketNoticeVO> getAnnouncementsByType(Integer typeId, Integer limit) {
        LambdaQueryWrapper<MarketNotice> queryWrapper = new LambdaQueryWrapper<>();

        if (typeId != null) {
            queryWrapper.eq(MarketNotice::getTypeId, typeId);
        }

        queryWrapper.orderByDesc(MarketNotice::getNoticeDate);

        if (limit != null) {
            queryWrapper.last("LIMIT " + limit);
        }

        List<MarketNotice> notices = marketNoticeMapper.selectList(queryWrapper);

        // 获取所有公告类型
        Map<Integer, String> typeMap = this.getAllNoticeTypesList().stream()
                .collect(Collectors.toMap(NoticeType::getId, NoticeType::getTypeName, (k1, k2) -> k1));

        // 转换为VO
        return notices.stream().map(notice -> {
            MarketNoticeVO vo = new MarketNoticeVO();
            BeanUtils.copyProperties(notice, vo);
            vo.setTypeName(typeMap.getOrDefault(notice.getTypeId(), ""));

            // 获取附件列表
            List<NoticeFile> files = noticeFileMapper.selectList(
                    Wrappers.<NoticeFile>lambdaQuery()
                            .eq(NoticeFile::getNoticeId, notice.getId())
            );
            vo.setAttachments(files);
            // 设置附件数量
            vo.setAttachmentCount(files != null ? files.size() : 0);
            assert files != null;
            if (!files.isEmpty() && files.get(0).getFileUrl() != null) {
                vo.setFileExistUrl(externalEndpoint + files.get(0).getFileUrl());
            } else {
                vo.setFileExistUrl(null);
            }

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<NoticeType> getAllNoticeTypesList() {
        LambdaQueryWrapper<NoticeType> queryWrapper = new LambdaQueryWrapper<>();
        return noticeTypeMapper.selectList(queryWrapper);
    }
}