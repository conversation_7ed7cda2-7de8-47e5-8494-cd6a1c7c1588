package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;
import org.jeecg.modules.api.power_trade.mapper.TradeCalendarRecordMapper;
import org.jeecg.modules.api.power_trade.service.ITradeCalendarRecordService;
import org.jeecg.modules.api.power_trade.service.ITradeDiaryFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 交易日历记录服务实现
 */
@Slf4j
@Service
public class TradeCalendarRecordServiceImpl extends ServiceImpl<TradeCalendarRecordMapper, TradeCalendarRecord> implements ITradeCalendarRecordService {

}