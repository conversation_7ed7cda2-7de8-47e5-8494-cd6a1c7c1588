<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.TradeDiaryMapper">

    <!-- 根据日期范围查询交易日历数据，并关联省份信息 -->
    <select id="selectTradeDiaryByDateRange" resultType="org.jeecg.modules.api.power_trade.entity.TradeDiary">
        SELECT * FROM trade_diary
        WHERE 1=1
        <!-- 确保所有比较操作符都正确转义 -->
        <if test="endDate != null">
            AND trade_date &lt;= #{endDate}
        </if>
        <if test="startDate != null">
            AND trade_date &gt;= #{startDate}
        </if>
        <if test="provinceIds != null and provinceIds.size() > 0">
            AND province_id IN
            <foreach collection="provinceIds" item="provinceId" open="(" separator="," close=")">
                #{provinceId}
            </foreach>
        </if>
        ORDER BY trade_date DESC
    </select>

    <!-- 根据日期和省份ID查询交易日历 -->
    <select id="selectTradeDiaryByDateAndProvince" resultType="org.jeecg.modules.api.power_trade.entity.TradeDiary">
        SELECT * FROM trade_diary
        WHERE 1=1
        <if test="targetDate != null">
            AND trade_date = #{targetDate}
        </if>
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        ORDER BY trade_date DESC
    </select>

    <!-- 分页查询省份的交易日历数据 -->
    <select id="getTradeCalendarByProvinceWithPage" resultType="java.util.Map">
        SELECT
        id,
        trade_date,
        province_id,
        province_name,
        trade_volume,
        trade_amount,
        create_time,
        update_time
        FROM trade_diary
        WHERE 1=1
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        ORDER BY trade_date DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 统计符合条件的总记录数 -->
    <select id="countTradeCalendarByProvince" resultType="int">
        SELECT COUNT(*) FROM trade_diary
        WHERE 1=1
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
    </select>

</mapper>