package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电站类型统计VO
 */
@Data
@ApiModel(value = "StationTypeStatisticsVO", description = "电站类型统计信息")
public class StationTypeStatisticsVO {

    @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)")
    private Integer stationType;

    @ApiModelProperty(value = "电站类型名称")
    private String stationTypeName;

    @ApiModelProperty(value = "电站数量")
    private Integer stationCount;

    @ApiModelProperty(value = "总容量(MW)")
    private BigDecimal totalCapacity;

    @ApiModelProperty(value = "平均容量(MW)")
    private BigDecimal avgCapacity;

    @ApiModelProperty(value = "总发电量(MWh)")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "平均电价(元/MWh)")
    private BigDecimal avgPrice;

    @ApiModelProperty(value = "图标URL")
    private String iconUrl;

    @ApiModelProperty(value = "占比(%)")
    private BigDecimal percentage;
}
