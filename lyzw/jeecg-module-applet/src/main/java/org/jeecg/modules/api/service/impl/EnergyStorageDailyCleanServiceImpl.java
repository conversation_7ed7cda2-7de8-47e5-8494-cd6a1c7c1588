package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyStorageDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.EnergyStorageDailyCleanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class EnergyStorageDailyCleanServiceImpl extends ServiceImpl<EnergyStorageDailyCleanMapper, EnergyStorageDailyClean> implements EnergyStorageDailyCleanService {
    
    @Override
    public List<EnergyStorageDailyClean> selectByDay(String stationId, String date) {
        return baseMapper.selectByDay(stationId, date);
    }
    
    @Override
    public List<EnergyStorageDailyClean> selectByMonth(Long stationId, String date) {
        try {
            log.info("开始按月查询储能日清分数据 - 电站ID: {}, 日期: {}", stationId, date);
            List<EnergyStorageDailyClean> result = baseMapper.selectByMonth(String.valueOf(stationId), date);
            log.info("按月查询完成 - 电站ID: {}, 日期: {}, 原始结果数量: {}",
                    stationId, date, result != null ? result.size() : 0);

            // 补全月度数据，确保每一天都有记录
            List<EnergyStorageDailyClean> completeResult = fillMissingDaysInMonth(stationId, date, result);

            log.info("数据补全完成 - 电站ID: {}, 日期: {}, 补全后数量: {}",
                    stationId, date, completeResult.size());

            return completeResult;
        } catch (Exception e) {
            log.error("按月查询储能日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<EnergyStorageDailyClean> selectByYear(String stationId, String date) {
        return baseMapper.selectByYear(stationId, date);
    }

    /**
     * 补全月度数据，确保每一天都有记录
     * 如果某天没有数据，则创建一个默认的0值记录
     */
    private List<EnergyStorageDailyClean> fillMissingDaysInMonth(Long stationId, String yearMonth, List<EnergyStorageDailyClean> existingData) {
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 获取该月的天数
            YearMonth ym = YearMonth.of(year, month);
            int daysInMonth = ym.lengthOfMonth();

            // 创建日期到数据的映射
            Map<String, EnergyStorageDailyClean> dataMap = new HashMap<>();
            if (existingData != null) {
                for (EnergyStorageDailyClean data : existingData) {
                    if (data.getDate() != null) {
                        String dateKey = data.getDate().toString();
                        dataMap.put(dateKey, data);
                    }
                }
            }

            // 生成完整的月度数据
            List<EnergyStorageDailyClean> completeData = new ArrayList<>();
            for (int day = 1; day <= daysInMonth; day++) {
                String dateStr = String.format("%04d-%02d-%02d", year, month, day);
                LocalDate currentDate = LocalDate.parse(dateStr);

                EnergyStorageDailyClean dayData = dataMap.get(dateStr);
                if (dayData != null) {
                    // 如果有数据，直接使用
                    completeData.add(dayData);
                } else {
                    // 如果没有数据，创建默认的0值记录
                    EnergyStorageDailyClean defaultData = createDefaultEnergyStorageDailyClean(stationId, currentDate);
                    completeData.add(defaultData);
                }
            }

            log.debug("月度数据补全完成 - 年月: {}, 原始数据: {}条, 补全后: {}条",
                    yearMonth, existingData != null ? existingData.size() : 0, completeData.size());

            return completeData;

        } catch (Exception e) {
            log.error("补全月度数据失败 - 年月: {}", yearMonth, e);
            return existingData != null ? existingData : new ArrayList<>();
        }
    }

    /**
     * 创建默认的EnergyStorageDailyClean记录（所有数值字段为0）
     */
    private EnergyStorageDailyClean createDefaultEnergyStorageDailyClean(Long stationId, LocalDate localDate) {
        EnergyStorageDailyClean defaultData = new EnergyStorageDailyClean();
        defaultData.setStationId(stationId);

        // 将LocalDate转换为Date
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        defaultData.setDate(date);

        // 设置所有数值字段为0
        defaultData.setUserDayAheadDeviationPower(BigDecimal.ZERO);
        defaultData.setUserDayAheadDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setUserDayAheadDeviationFee(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationPower(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setUserRealtimeDeviationFee(BigDecimal.ZERO);
        defaultData.setUserTotalPower(BigDecimal.ZERO);
        defaultData.setUserTotalFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setPowerGenerationDayAheadDeviationFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationAveragePrice(BigDecimal.ZERO);
        defaultData.setPowerGenerationRealtimeDeviationFee(BigDecimal.ZERO);
        defaultData.setPowerGenerationTotalPower(BigDecimal.ZERO);
        defaultData.setPowerGenerationTotalFee(BigDecimal.ZERO);

        defaultData.setCreateTime(new Date());
        defaultData.setUpdateTime(new Date());
        return defaultData;
    }
}