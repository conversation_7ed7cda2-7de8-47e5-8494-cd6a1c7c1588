package org.jeecg.modules.api.power.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发电趋势数据DTO
 */
@Data
@ApiModel("发电趋势数据DTO")
public class PowerGenerationTrendDto {

    @ApiModelProperty(value = "时间标签")
    private String timeLabel;

    @ApiModelProperty(value = "发电量")
    private Double powerGeneration;

    @ApiModelProperty(value = "实际功率")
    private Double actualPower;
}