package org.jeecg.modules.api.power.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发电趋势数据DTO
 */
@Data
@ApiModel("发电趋势数据DTO")
public class PowerGenerationTrendDto {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型")
    private Integer stationType;

    @ApiModelProperty(value = "时间标签")
    private String timeLabel;

    @ApiModelProperty(value = "发电量")
    private Double powerGeneration;

    @ApiModelProperty(value = "实际功率")
    private Double actualPower;
}