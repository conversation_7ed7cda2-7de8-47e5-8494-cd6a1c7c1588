<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.StationMapper">

    <!-- 根据电站ID列表聚合查询交易指标数据 -->
    <select id="getAggregatedTradeDataByStationIds" resultType="org.jeecg.modules.api.power_trade.vo.PowerDashboardVO">
        SELECT
            SUM(sts.current_month_power) as accumulatedPower,
            SUM(sts.current_month_plan_power) as plannedPower,
            AVG(sts.settlement_average_price) as settlementAvgPrice,
            SUM(sts.settle_power) as settlementPower,
            SUM(sts.limited_power) as limitedPower,
            AVG(sts.bench_mark_electricity_price) as benchmarkPrice
        FROM screen_trade_settlement sts
        WHERE sts.station_id IN
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        AND sts.year = #{year}
        AND sts.month = #{month}
    </select>

    <!-- 根据区域代码聚合查询交易指标数据 -->
    <select id="getAggregatedTradeDataByRegion" resultType="org.jeecg.modules.api.power_trade.vo.PowerDashboardVO">
        SELECT
            SUM(sts.current_month_power) as accumulatedPower,
            SUM(sts.current_month_plan_power) as plannedPower,
            AVG(sts.settlement_average_price) as settlementAvgPrice,
            SUM(sts.settle_power) as settlementPower,
            SUM(sts.limited_power) as limitedPower,
            AVG(sts.bench_mark_electricity_price) as benchmarkPrice
        FROM screen_trade_settlement sts
        INNER JOIN station s ON sts.station_id = s.id
        WHERE s.province_id = #{provinceId}
        AND sts.year = #{year}
        AND sts.month = #{month}
    </select>

    <!-- 查询电站类型统计信息 -->
    <select id="getStationTypeStatistics" resultType="org.jeecg.modules.api.power_trade.vo.StationTypeStatisticsVO">
        SELECT
            s.type as stationType,
            COUNT(s.id) as stationCount,
            SUM(sts.current_month_power) as totalPower,
            SUM(sts.settle_power) as totalSettlePower,
            AVG(sts.settlement_average_price) as avgPrice
        FROM station s
        LEFT JOIN screen_trade_settlement sts ON s.id = sts.station_id
            AND sts.year = #{year} AND sts.month = #{month}
        WHERE s.id IN
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        GROUP BY s.type
    </select>

    <!-- 根据区域代码查询电站ID列表 -->
    <select id="getStationIdsByProvinceId" resultType="java.lang.Long">
        SELECT id FROM station WHERE province_id = #{provinceId}
    </select>

    <!-- 查询指定电站的基本信息和容量 -->
    <select id="getStationBasicInfoByIds" resultType="org.jeecg.modules.api.power_trade.entity.Station">
        SELECT * FROM station
        WHERE id IN
        <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
    </select>

</mapper>