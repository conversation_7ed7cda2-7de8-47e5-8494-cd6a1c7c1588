package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@ApiModel("场站类型统计信息DTO")
@Data
@AllArgsConstructor
public class StationTypeStatisticsDTO {
    @ApiModelProperty(value = "交易容量(MW)", notes = "所有参与交易场站的总容量")
    private double tradingCapacity;

    @ApiModelProperty(value = "场站总数", notes = "参与交易的总场站数量")
    private int stationCount;

    @ApiModelProperty(value = "风电场站数量", notes = "类型为风电的场站数量")
    private int windStationCount;

    @ApiModelProperty(value = "光伏场站数量", notes = "类型为光伏的场站数量")
    private int solarStationCount;

    @ApiModelProperty(value = "储能场站数量", notes = "类型为储能的场站数量")
    private int storageStationCount;

    @ApiModelProperty(value = "累计发电量(MWh)", notes = "当月累计发电量")
    private double accumulatedPower;

    @ApiModelProperty(value = "计划发电量(MWh)", notes = "当月计划发电量")
    private double plannedPower;

    @ApiModelProperty(value = "结算均价(元/MWh)", notes = "当月电力结算平均价格")
    private double settlementAvgPrice;

    @ApiModelProperty(value = "结算电量(MWh)", notes = "当月已结算电量")
    private double settlementPower;

    @ApiModelProperty(value = "限电量(MWh)", notes = "当月受限电量")
    private double limitedPower;

    @ApiModelProperty(value = "目标电价")
    private double targetPowerPrice;

    @ApiModelProperty(value = "燃煤标杆电价")
    private double benchmarkPrice;
}