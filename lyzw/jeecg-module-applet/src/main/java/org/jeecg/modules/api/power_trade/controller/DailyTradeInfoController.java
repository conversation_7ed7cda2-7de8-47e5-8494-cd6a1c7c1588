package org.jeecg.modules.api.power_trade.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.power_trade.service.DailyTradeInfoService;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power_trade.vo.DailyTradeInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 日交易信息查询接口
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@RestController
@RequestMapping("/api/power_trade/daily_trade")
@Api(tags = "日交易信息接口")
@Slf4j
public class DailyTradeInfoController {

    @Autowired
    private DailyTradeInfoService dailyTradeInfoService;

    /**
     * 查询日交易信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询日交易信息", notes = "支持按省份、日期范围、电站类型筛选，默认查询当月数据")
    public Result<List<DailyTradeInfoVO>> getDailyTradeInfo(
            @ApiParam(value = "省份ID (0-全国, 1-江苏, 2-安徽)", required = false) 
            @RequestParam(defaultValue = "0") Integer provinceId,
            
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd", required = false) 
            @RequestParam(required = false) String startDate,
            
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd", required = false) 
            @RequestParam(required = false) String endDate,
            
            @ApiParam(value = "电站类型 (photovoltaic-光伏, wind-风电, storage-储能)", required = false) 
            @RequestParam(required = false) String stationType) {

        try {
            // 参数验证
            Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
                ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
                
                // 验证省份ID
                validator.validateProvinceId(provinceId);
                
                // 验证电站类型
                if (stationType != null && !stationType.trim().isEmpty()) {
                    validateStationType(stationType);
                }
                
                // 验证日期格式和范围
                if (startDate != null && !startDate.trim().isEmpty()) {
                    validator.validateDateFormat(startDate);
                }
                if (endDate != null && !endDate.trim().isEmpty()) {
                    validator.validateDateFormat(endDate);
                }
                
                // 验证日期范围逻辑
                validateDateRange(startDate, endDate);
            });

            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }

            // 设置默认日期范围（当月）
            String finalStartDate = startDate;
            String finalEndDate = endDate;
            
            if ((startDate == null || startDate.trim().isEmpty()) && 
                (endDate == null || endDate.trim().isEmpty())) {
                // 默认查询当月数据
                LocalDate now = LocalDate.now();
                LocalDate firstDayOfMonth = now.withDayOfMonth(1);
                finalStartDate = firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                finalEndDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                
                log.info("使用默认日期范围 - 开始日期: {}, 结束日期: {}", finalStartDate, finalEndDate);
            } else if (startDate == null || startDate.trim().isEmpty()) {
                // 只有结束日期，开始日期设为当月第一天
                LocalDate endLocalDate = LocalDate.parse(endDate);
                finalStartDate = endLocalDate.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (endDate == null || endDate.trim().isEmpty()) {
                // 只有开始日期，结束日期设为今天
                finalEndDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            log.info("查询日交易信息 - 省份ID: {}, 开始日期: {}, 结束日期: {}, 电站类型: {}", 
                    provinceId, finalStartDate, finalEndDate, stationType);

            // 调用Service层查询数据
            List<DailyTradeInfoVO> result = dailyTradeInfoService.getDailyTradeInfo(
                    provinceId, finalStartDate, finalEndDate, stationType);

            log.info("查询完成 - 返回记录数: {}", result != null ? result.size() : 0);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询日交易信息失败 - 省份ID: {}, 开始日期: {}, 结束日期: {}, 电站类型: {}", 
                     provinceId, startDate, endDate, stationType, e);
            return Result.error("查询日交易信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证电站类型
     */
    private void validateStationType(String stationType) {
        if (stationType == null || stationType.trim().isEmpty()) {
            return;
        }
        
        String normalizedType = stationType.trim().toLowerCase();
        if (!"photovoltaic".equals(normalizedType) && 
            !"wind".equals(normalizedType) && 
            !"storage".equals(normalizedType)) {
            throw new ParamValidationUtil.ValidationException(
                "电站类型参数错误，支持的类型：photovoltaic(光伏)、wind(风电)、storage(储能)");
        }
    }

    /**
     * 验证日期范围
     */
    private void validateDateRange(String startDate, String endDate) {
        if (startDate == null || startDate.trim().isEmpty() || 
            endDate == null || endDate.trim().isEmpty()) {
            return; // 如果有空值，跳过范围验证
        }

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            // 验证开始日期不能晚于结束日期
            if (start.isAfter(end)) {
                throw new ParamValidationUtil.ValidationException("开始日期不能晚于结束日期");
            }
            
            // 验证日期范围不能超过3个月
            if (start.plusMonths(3).isBefore(end)) {
                throw new ParamValidationUtil.ValidationException("查询日期范围不能超过3个月");
            }
            
            // 验证不能查询未来日期
            LocalDate today = LocalDate.now();
            if (start.isAfter(today)) {
                throw new ParamValidationUtil.ValidationException("开始日期不能是未来日期");
            }
            if (end.isAfter(today)) {
                throw new ParamValidationUtil.ValidationException("结束日期不能是未来日期");
            }
            
        } catch (Exception e) {
            if (e instanceof ParamValidationUtil.ValidationException) {
                throw e;
            }
            throw new ParamValidationUtil.ValidationException("日期格式错误，应为yyyy-MM-dd格式");
        }
    }
}
