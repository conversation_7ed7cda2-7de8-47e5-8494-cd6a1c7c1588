package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.UserFront;
import org.jeecg.modules.api.power_trade.mapper.UserFrontMapper;
import org.jeecg.modules.api.power_trade.service.UserFrontService;
import org.springframework.stereotype.Service;

@Service
public class UserFrontServiceImpl extends ServiceImpl<UserFrontMapper, UserFront> implements UserFrontService {
}
