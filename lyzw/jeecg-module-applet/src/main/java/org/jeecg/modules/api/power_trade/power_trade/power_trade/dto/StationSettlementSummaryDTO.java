package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "StationSettlementSummaryDTO", description = "电站结算汇总数据")
public class StationSettlementSummaryDTO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型(1:风电, 2:光伏, 3:储能)")
    private Integer stationType;

    @ApiModelProperty(value = "累计结算电量(MWh)")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "累计结算电费(元)")
    private BigDecimal totalSettlementFee;

    @ApiModelProperty(value = "交易均价(元/MWh)")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "查询时间范围")
    private String queryPeriod;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;
}