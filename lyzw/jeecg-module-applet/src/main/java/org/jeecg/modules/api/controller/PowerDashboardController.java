package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power_trade.dto.*;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 电力交易首页控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-首页接口")
@RequestMapping("/api/power_trade/dashboard")
public class PowerDashboardController {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementMapper screenTradeSettlementMapper;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    @Autowired
    private PowerDashboardService powerDashboardService;

    /**
     * 获取首页数据
     */
    @GetMapping("/summary")
    @ApiOperation(value = "获取首页概览数据", notes = "获取区域电力交易概况数据")
    public Result<DashboardSummaryDTO> getDashboard(
            @ApiParam(value = "省份Id") @RequestParam Integer provinceId) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create().validateProvinceId(provinceId);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        // 检查是否为全国数据源汇总
        if (provinceId == 0) {
            // 全国数据源汇总模式
            Map<String, Object> aggregatedData = multiDataSourceAggregationService.aggregateAllProvincesDashboardSummary();
            // 这里需要将Map转换为DashboardSummaryDTO
            DashboardSummaryDTO result = convertToDashboardSummaryDTO(aggregatedData);
            return Result.OK(result);
        }

        // 单省份模式
        DashboardSummaryDTO result = new DashboardSummaryDTO();
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 1. 获取指定省份且已参与交易的所有电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getTradeStatus, 1)
                    .eq(Station::getProvinceId, provinceId);

            List<Station> stationList = stationService.list(stationQuery);

            // 若无电站，直接返回空DTO
            if (stationList == null || stationList.isEmpty()) {
                return Result.OK(result);
            }

            // 2. 计算交易容量
            double tradingCapacity = stationList.stream()
                    .mapToDouble(Station::getCapacity)
                    .sum();

            // 储能站功率单独累加
            double storagePower = stationList.stream()
                    .filter(s -> s.getType() != null && s.getType() == 3)
                    .mapToDouble(station -> station.getPower() != null ? station.getPower() : 0.0)
                    .sum();
            tradingCapacity += storagePower;

            // 3. 统计各类型场站数量
            int windCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 1).count();
            int solarCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 2).count();
            int storageCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 3).count();

            // 4. 统计能源类型及其数量
            Map<Integer, Integer> energyTypeCountMap = stationList.stream()
                    .filter(s -> s.getType() != null)
                    .collect(Collectors.groupingBy(
                            Station::getType,
                            Collectors.summingInt(e -> 1)));

            // 5. 汇总结算数据
            double accumulatedPower = 0.0; // 累计发电量
            double plannedPower = 0.0; // 计划发电量
            double settlementAvgPrice = 0.0; // 结算均价
            double settlementPower = 0.0; // 结算电量
            double limitedPower = 0.0; // 限电量
            int settlementCount = 0; // 有效结算数据数量
            double benchmarkPrice = 0.0;
            double targetPowerPrice = 0.0;

            switch (provinceId) {
                case 1:
                    benchmarkPrice = 384.4;
                    targetPowerPrice = 384.4;
                    break;
                case 2:
                    benchmarkPrice = 391;
                    targetPowerPrice = 391;
                    break;
            }

            // 切换到省份数据源查询结算数据
            dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey != null) {
                log.info("切换到数据源: {} 查询结算数据", dsKey);
                DynamicDataSourceContextHolder.push(dsKey);

                try {
                    // 获取该省份所有电站的ID列表
                    List<Long> stationIds = stationList.stream()
                            .map(Station::getId)
                            .collect(Collectors.toList());

                    log.info("查询首页概览数据，省份ID: {}, 电站数量: {}, 电站IDs: {}",
                            provinceId, stationIds.size(), stationIds);

                    // 先调试检查表中的数据情况
                    Map<String, Object> debugInfo = screenTradeSettlementMapper.debugTableData(stationIds);
                    log.info("表数据调试信息: {}", debugInfo);

                    List<Map<String, Object>> debugDetails = screenTradeSettlementMapper.debugDetailData(stationIds);
                    log.info("表详细数据（前10条）: {}", debugDetails);

                    // 查询最新的结算数据（自动获取最新年月的数据）
                    Map<String, Object> summaryData = screenTradeSettlementMapper
                            .selectDashboardSummaryData(stationIds);

                    if (summaryData != null && !summaryData.isEmpty()) {
                        log.info("查询到概览数据: {}", summaryData);

                        // 设置查询到的真实数据
                        accumulatedPower = convertToDouble(summaryData.get("accumulatedPower"));
                        plannedPower = convertToDouble(summaryData.get("plannedPower"));
                        settlementAvgPrice = convertToDouble(summaryData.get("settlementAvgPrice"));
                        settlementPower = convertToDouble(summaryData.get("settlementPower"));
                        limitedPower = convertToDouble(summaryData.get("limitedPower"));
                        settlementCount = stationIds.size(); // 设置为电站数量
                    } else {
                        log.warn("未查询到概览数据，使用默认值");
                    }
                } catch (Exception e) {
                    log.error("查询首页概览数据失败，使用默认值: {}", e.getMessage(), e);
                } finally {
                    DynamicDataSourceContextHolder.clear();
                }
            } else {
                log.warn("不支持的省份ID: {}, 使用默认值", provinceId);
            }

            // 6. 构建并设置DTO值
            result.setStationTypeStatistics(new StationTypeStatisticsDTO(
                    tradingCapacity,
                    stationList.size(),
                    windCount,
                    solarCount,
                    storageCount,
                    accumulatedPower,
                    plannedPower,
                    settlementCount == 0 ? 0.0 : settlementAvgPrice / settlementCount,
                    settlementPower,
                    limitedPower,
                    targetPowerPrice,
                    benchmarkPrice));
            result.setEnergyTypeCount(energyTypeCountMap);

            return Result.OK(result);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @GetMapping("/getStation")
    @ApiOperation("获取对应省份的电站")
    public Result<List<Station>> getStationList(@ApiParam(value = "省份ID") @RequestParam Integer provinceId) {
        LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
        if (provinceId == 0) {
            return Result.OK(stationService.list());
        }
        stationQuery.eq(Station::getProvinceId, provinceId);
        return Result.OK(stationService.getStationsByProvinceId(provinceId));
    }

    /**
     * 获取电站交易概况
     */
    @GetMapping("/settlement/summary")
    @ApiOperation(value = "获取电站交易概况", notes = "返回电站列表的交易概况，每个电站包含电站名称、累计结算电量、交易均价")
    public Result<List<SettlementSummaryDTO>> getSettlementSummary(
            @ApiParam(value = "电站ID（可选，为空时返回省份汇总数据）") @RequestParam(required = false) Long stationId,
            @ApiParam(value = "省份ID") @RequestParam Integer provinceId,
            @ApiParam(value = "查询维度: 1-月度 2-年度") @RequestParam(required = false, defaultValue = "1") Integer dimension,
            @ApiParam(value = "月份(格式: yyyy-MM)，仅在dimension=1时有效") @RequestParam(required = false) String month,
            @ApiParam(value = "年份(格式: yyyy)，仅在dimension=2时有效") @RequestParam(required = false) String year) {

        // 参数验证和格式化
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
            validator.validateProvinceId(provinceId);
            validator.validateQueryDimension(dimension);
            if (dimension == 1) {
                // 月度查询：支持month=yyyy-MM或month=MM+year=yyyy的组合
                if (month != null && month.matches("^\\d{4}-\\d{2}$")) {
                    // 格式：yyyy-MM，直接验证
                    validator.validateMonthQuery(month);
                } else if (month != null && year != null &&
                          month.matches("^(0[1-9]|1[0-2])$") && year.matches("^\\d{4}$")) {
                    // 格式：MM + yyyy，验证两个参数
                    validator.validateMonthQuery(month);
                    validator.validateYearQuery(year);
                } else {
                    throw new ParamValidationUtil.ValidationException("月度查询需要month参数(格式:yyyy-MM)或month+year参数组合(格式:MM+yyyy)");
                }
            } else {
                validator.validateYearQuery(year);
            }
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        try {
            // 格式化月份参数：如果是MM+yyyy格式，转换为yyyy-MM
            String formattedMonth = month;
            if (dimension == 1 && month != null && year != null &&
                month.matches("^(0[1-9]|1[0-2])$") && year.matches("^\\d{4}$")) {
                formattedMonth = year + "-" + month;
                log.info("格式化月份参数：month={}, year={} -> formattedMonth={}", month, year, formattedMonth);
            }

            List<SettlementSummaryDTO> result = powerDashboardService.getSettlementSummary(
                    stationId, provinceId, dimension, formattedMonth, year);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取电站交易概况失败 - 省份ID: {}, 电站ID: {}", provinceId, stationId, e);
            return Result.error("获取电站交易概况失败: " + e.getMessage());
        }
    }

    /**
     * 电量电价
     */
    @ApiOperation(value = "电量电价", notes = "安徽查日前节点的出清电量电价。江苏查实时节点出清电量和电价")
    @GetMapping("/getAhuFieldOptimized")
    public Result<?> getAhuFieldOptimized(
            @ApiParam(value = "省份ID，0表示根据电站ID自动获取省份") @RequestParam Integer provinceId,
            @ApiParam(value = "电站ID") @RequestParam(required = false) Long stationId,
            @ApiParam(value = "日期") @RequestParam String date,
            @ApiParam(value = "维度: 1-年度, 2-月度, 3-日度") @RequestParam(defaultValue = "3") String dimension) {

        long startTime = System.currentTimeMillis();

        try {
            log.info("电量电价查询开始 - 省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}",
                    provinceId, stationId, date, dimension);

            // 委托给Service层处理业务逻辑
            Object result = powerDashboardService.getElectricityPriceData(provinceId, stationId, date, dimension);

            long queryTime = System.currentTimeMillis() - startTime;
            log.info("电量电价查询完成 - 省份ID: {}, 电站ID: {}, 查询耗时: {}ms, 结果类型: {}",
                    provinceId, stationId, queryTime, result != null ? result.getClass().getSimpleName() : "null");

            return Result.OK(result);

        } catch (IllegalArgumentException e) {
            log.error("电量电价查询参数错误 - 省份ID: {}, 电站ID: {}, 错误: {}", provinceId, stationId, e.getMessage());
            return Result.error("参数错误: " + e.getMessage());

        } catch (Exception e) {
            long queryTime = System.currentTimeMillis() - startTime;
            log.error("电量电价查询系统异常 - 省份ID: {}, 电站ID: {}, 查询耗时: {}ms, 错误: {}",
                    provinceId, stationId, queryTime, e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取省份列表", notes = "从station中获取所有省份信息")
    @GetMapping("/provinces")
    public Result<List<ProvinceDTO>> getProvinces() {
        try {
            Map<Integer, String> allDataSources = Optional.ofNullable(ProvinceDataSourceUtil.getAllProvinceDataSource())
                    .orElse(Collections.emptyMap());

            List<ProvinceDTO> provinceList = allDataSources.entrySet().stream()
                    .map(entry -> {
                        Integer provinceId = entry.getKey();
                        String dataSource = entry.getValue();
                        String provinceName = ProvinceDataSourceUtil.getProvinceName(provinceId);
                        ProvinceDTO dto = new ProvinceDTO();
                        dto.setProvinceId(provinceId);
                        dto.setDataSource(dataSource);
                        dto.setProvinceName(provinceName);
                        return dto;
                    })
                    .sorted(Comparator.comparing(ProvinceDTO::getProvinceId))
                    .collect(Collectors.toList());

            return Result.OK(provinceList);
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return Result.error("获取省份列表失败: " + (e.getMessage() != null ? e.getMessage() : "未知错误"));
        }
    }


    /**
     * 安全转换为Integer
     */
    private Integer convertToInteger(Object value) {
        if (value == null)
            return null;
        if (value instanceof Integer)
            return (Integer) value;
        if (value instanceof Long)
            return ((Long) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }


    /**
     * 将Object转换为Double，处理null值和类型转换
     *
     * @param value 待转换的值
     * @return 转换后的Double值，null时返回0.0
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法转换为Double: {}", value);
            return 0.0;
        }
    }

    /**
     * 将汇总数据转换为DashboardSummaryDTO
     */
    private DashboardSummaryDTO convertToDashboardSummaryDTO(Map<String, Object> aggregatedData) {
        DashboardSummaryDTO result = new DashboardSummaryDTO();

        if (aggregatedData == null || aggregatedData.isEmpty()) {
            return result;
        }

        // 创建电站类型统计数据
        StationTypeStatisticsDTO statistics = new StationTypeStatisticsDTO(
                convertToDouble(aggregatedData.get("totalCapacity")), // tradingCapacity
                convertToInteger(aggregatedData.get("totalStations")), // stationCount
                convertToInteger(aggregatedData.get("windStationCount")), // windStationCount
                convertToInteger(aggregatedData.get("solarStationCount")), // solarStationCount
                convertToInteger(aggregatedData.get("storageStationCount")), // storageStationCount
                convertToDouble(aggregatedData.get("totalPowerGeneration")), // accumulatedPower
                convertToDouble(aggregatedData.get("plannedPower")), // plannedPower
                convertToDouble(aggregatedData.get("avgPrice")), // settlementAvgPrice
                convertToDouble(aggregatedData.get("settlementPower")), // settlementPower
                convertToDouble(aggregatedData.get("limitedPower")), // limitedPower
                convertToDouble(aggregatedData.get("targetPowerPrice")), // targetPowerPrice
                convertToDouble(aggregatedData.get("benchmarkPrice")) // benchmarkPrice
        );

        result.setStationTypeStatistics(statistics);

        // 设置能源类型统计（如果有的话）
        @SuppressWarnings("unchecked")
        Map<Integer, Integer> energyTypeCount = (Map<Integer, Integer>) aggregatedData.get("energyTypeCount");
        if (energyTypeCount != null) {
            result.setEnergyTypeCount(energyTypeCount);
        } else {
            result.setEnergyTypeCount(new HashMap<>());
        }

        return result;
    }

    @ApiOperation(value = "发电趋势数据查询", notes = "根据时间维度和场站获取发电趋势数据")
    @GetMapping("/power/trend")
    public Result<Map<String, Object>> getPowerGenerationTrend(
            @ApiParam(value = "省份ID (1-安徽, 2-江苏)", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "时间维度 (1-年度, 2-月度, 3-日度)", required = true) @RequestParam String dimension,
            @ApiParam(value = "场站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "查询日期/年月/年份", required = true) @RequestParam String date) {

        try {
            log.info("开始查询发电趋势数据 - 省份ID: {}, 维度: {}, 场站ID: {}, 日期: {}",
                    provinceId, dimension, stationId, date);

            // 参数验证
            if (!Arrays.asList("1", "2", "3").contains(dimension)) {
                return Result.error("时间维度参数错误，支持：1-年度, 2-月度, 3-日度");
            }

            // 获取数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }

            DynamicDataSourceContextHolder.push(dsKey);
            try {
                Map<String, Object> result = powerDashboardService.getPowerGenerationTrend(
                        provinceId, dimension, stationId, date);

                log.info("发电趋势数据查询成功 - 场站ID: {}, 数据点数: {}",
                        stationId, result.get("dataCount"));

                return Result.OK(result);

            } finally {
                DynamicDataSourceContextHolder.clear();
            }

        } catch (Exception e) {
            log.error("查询发电趋势数据失败 - 场站ID: {}, 维度: {}, 日期: {}",
                    stationId, dimension, date, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}