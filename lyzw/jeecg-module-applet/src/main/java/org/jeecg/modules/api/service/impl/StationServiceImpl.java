package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.mapper.StationMapper;
import org.jeecg.modules.api.power_trade.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电力交易电站服务实现类
 */
@Service
public class StationServiceImpl extends ServiceImpl<StationMapper, Station> implements StationService {

    @Autowired
    private ScreenTradeSettlementMapper screenTradeSettlementMapper;

    @Override
    public List<Station> getStationsByProvinceId(Integer provinceId) {
        LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Station::getProvinceId, provinceId);
        return this.list(queryWrapper);
    }

}