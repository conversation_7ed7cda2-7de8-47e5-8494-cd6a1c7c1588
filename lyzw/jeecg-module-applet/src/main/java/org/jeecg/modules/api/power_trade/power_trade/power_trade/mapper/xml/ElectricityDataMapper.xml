<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.ElectricityDataMapper">
    <!-- 根据省份ID动态查询电量数据 -->
    <select id="selectElectricityDataByProvince" resultType="org.jeecg.modules.api.power_trade.dto.ElectricityDataDTO">
        <choose>
            <!-- 安徽省：查询日前节点出清电量表，并关联电价表 -->
            <when test="provinceId == 1">
                SELECT
                e.id,
                e.date,
                e.time,
                e.value,
                e.station_id as stationId,
                e.station_name as stationName,
                e.province_id as provinceId,
                'day_ahead_node_clear_electricity' as sourceTable,
                p.value as price
                FROM day_ahead_node_clear_electricity e
                LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id
                    AND e.date = p.date
                    AND e.time = p.time
                WHERE 1=1
                <if test="stationId != null">
                    AND e.station_id = #{stationId}
                </if>
                <choose>
                    <when test='dimension == "3"'>
                        AND e.date = #{date}
                    </when>
                    <when test='dimension == "2"'>
                        AND DATE_FORMAT(e.date, '%Y-%m') = #{date}
                    </when>
                    <when test='dimension == "1"'>
                        AND DATE_FORMAT(e.date, '%Y') = #{date}
                    </when>
                    <otherwise>
                        AND 1=0
                    </otherwise>
                </choose>
                ORDER BY e.date, e.time
                LIMIT 100
            </when>

            <!-- 江苏省：查询实时出清电量表，并关联电价表 -->
            <when test="provinceId == 2">
                SELECT
                e.id,
                e.date,
                e.time,
                e.value,
                NULL as stationId,
                '江苏实时出清' as stationName,
                #{provinceId} as provinceId,
                'realtime_clear_power' as sourceTable,
                NULL as price
                FROM realtime_clear_power e
                WHERE 1=1
                AND e.province_id = #{provinceId}
                <if test="stationId != null">
                    AND e.station_id = #{stationId}
                </if>
                <choose>
                    <when test='dimension == "3"'>
                        AND DATE(e.date) = DATE(#{date})
                    </when>
                    <when test='dimension == "2"'>
                        AND DATE_FORMAT(e.date, '%Y-%m') = #{date}
                    </when>
                    <when test='dimension == "1"'>
                        AND DATE_FORMAT(e.date, '%Y') = #{date}
                    </when>
                    <otherwise>
                        AND 1=0
                    </otherwise>
                </choose>
                ORDER BY e.date, e.time
                LIMIT 100
            </when>

            <otherwise>
                SELECT NULL as id WHERE 1=0
            </otherwise>
        </choose>
    </select>

    <!-- 按维度分组统计电量数据（同时包含电价统计） -->
    <select id="selectElectricityDataGrouped" resultType="java.util.Map">
        <choose>
            <!-- 安徽省统计（包含电价统计） -->
            <when test="provinceId == 1">
                <choose>
                    <when test='dimension == "3"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m-%d') as date,
                        e.time,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        AVG(IFNULL(p.value, 0)) as avgPrice,
                        SUM(IFNULL(e.value, 0) * IFNULL(p.value, 0)) as totalFee
                        FROM day_ahead_node_clear_electricity e
                        LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id
                            AND e.date = p.date
                            AND e.time = p.time
                        WHERE 1=1
                        <if test="stationId != null">
                            AND e.station_id = #{stationId}
                        </if>
                        AND e.date = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m-%d'), e.time
                        ORDER BY date, e.time
                    </when>
                    <when test='dimension == "2"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m-%d') as date,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        AVG(IFNULL(p.value, 0)) as avgPrice,
                        SUM(IFNULL(e.value, 0) * IFNULL(p.value, 0)) as totalFee
                        FROM day_ahead_node_clear_electricity e
                        LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id
                            AND e.date = p.date
                            AND e.time = p.time
                        WHERE 1=1
                        <if test="stationId != null">
                            AND e.station_id = #{stationId}
                        </if>
                        AND DATE_FORMAT(e.date, '%Y-%m') = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m-%d')
                        ORDER BY date
                    </when>
                    <when test='dimension == "1"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m') as month,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        AVG(IFNULL(p.value, 0)) as avgPrice,
                        SUM(IFNULL(e.value, 0) * IFNULL(p.value, 0)) as totalFee
                        FROM day_ahead_node_clear_electricity e
                        LEFT JOIN day_ahead_node_clear_price p ON e.station_id = p.station_id
                            AND e.date = p.date
                            AND e.time = p.time
                        WHERE 1=1
                        <if test="stationId != null">
                            AND e.station_id = #{stationId}
                        </if>
                        AND DATE_FORMAT(e.date, '%Y') = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m')
                        ORDER BY month
                    </when>
                </choose>
            </when>

            <!-- 江苏省统计（包含电价统计） -->
            <when test="provinceId == 2">
                <choose>
                    <when test='dimension == "3"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m-%d') as date,
                        e.time,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        0 as avgPrice,
                        0 as totalFee
                        FROM realtime_clear_power e
                        WHERE 1=1
                        AND e.date = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m-%d'), e.time
                        ORDER BY date, e.time
                    </when>
                    <when test='dimension == "2"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m-%d') as date,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        0 as avgPrice,
                        0 as totalFee
                        FROM realtime_clear_power e
                        WHERE 1=1
                        AND DATE_FORMAT(e.date, '%Y-%m') = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m-%d')
                        ORDER BY date
                    </when>
                    <when test='dimension == "1"'>
                        SELECT
                        DATE_FORMAT(e.date, '%Y-%m') as month,
                        SUM(IFNULL(e.value, 0)) as totalValue,
                        0 as avgPrice,
                        0 as totalFee
                        FROM realtime_clear_power e
                        WHERE 1=1
                        AND DATE_FORMAT(e.date, '%Y') = #{date}
                        GROUP BY DATE_FORMAT(e.date, '%Y-%m')
                        ORDER BY month
                    </when>
                    <otherwise>
                        SELECT '' as month, 0 as totalValue, 0 as avgPrice, 0 as totalFee WHERE 1=0
                    </otherwise>
                </choose>
            </when>

            <!-- 其他省份或无效查询的默认处理 -->
            <otherwise>
                SELECT '' as date, 0 as totalValue, 0 as avgPrice, 0 as totalFee WHERE 1=0
            </otherwise>
        </choose>
    </select>

</mapper>