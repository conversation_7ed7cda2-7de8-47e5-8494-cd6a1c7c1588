package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 电站查询参数
 */
@Data
public class StationQueryParam {

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 月份 (YYYY-MM)
     */
    private String month;

    /**
     * 电站名称（模糊查询）
     */
    private String name;

    /**
     * 电站类型 (1-风电, 2-光伏, 3-储能)
     */
    private Integer type;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;
} 