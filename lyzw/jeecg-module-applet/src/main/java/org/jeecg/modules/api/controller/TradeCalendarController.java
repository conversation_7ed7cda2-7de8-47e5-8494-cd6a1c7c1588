package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.service.ITradeCalendarRecordService;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易日历控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-交易日历")
@RequestMapping("/api/power_trade/calendar")
public class TradeCalendarController {

    @Autowired
    private ITradeCalendarRecordService tradeCalendarRecordService;

    /**
     * 查询交易日历数据
     *
     * @param date       日期 yyyy-MM-dd（可选）
     * @param provinceId 省份ID (0-全国汇总, 1-江苏, 2-安徽)
     * @return 交易日历数据
     */
    @GetMapping("/queryByDate")
    @ApiOperation(value = "查询交易日历",
            notes = "根据日期和省份ID查询交易日历数据")
    public Result<List<TradeCalendarRecord>> queryByDate(
            @ApiParam(value = "日期，格式: yyyy-MM-dd（可选，不传则返回所有日期）")
            @RequestParam(required = false) String date,
            @ApiParam(value = "省份ID (0-全国汇总, 1-江苏, 2-安徽)", required = true)
            @RequestParam Integer provinceId) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create().validateProvinceId(provinceId);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        log.info("查询交易日历 - 省份ID: {}, 日期: {}", provinceId, date != null ? date : "全部");

        try {
            List<TradeCalendarRecord> result;

            if (provinceId == 0) {
                // 全国汇总模式：聚合所有省份的数据
                result = queryAllProvincesTradeCalendar(date);
            } else {
                // 单省份模式：查询指定省份的数据
                result = querySingleProvinceTradeCalendar(date, provinceId);
            }

            log.info("查询完成 - 省份ID: {}, 返回记录数: {}", provinceId,
                    result.size());

            return Result.OK(result);
        } catch (Exception e) {
            log.error("查询交易日历失败 - 省份ID: {}, 日期: {}", provinceId, date, e);
            return Result.error("查询交易日历失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有省份的交易日历汇总数据
     */
    private List<TradeCalendarRecord> queryAllProvincesTradeCalendar(String date) {
        log.info("开始查询全国交易日历汇总数据 - 日期: {}", date != null ? date : "全部");

        List<TradeCalendarRecord> allRecords = new ArrayList<>();

        Map<Integer,String> provinceDataSourceMap = ProvinceDataSourceUtil.getAllProvinceDataSource();

// 查询所有支持的省份
        List<Integer> supportedProvinces = provinceDataSourceMap.keySet().stream()
                .sorted(Comparator.naturalOrder())
                .filter(provinceId -> provinceId != 0)  // 排除全国汇总
                .collect(Collectors.toList());

        supportedProvinces.forEach(provinceId -> {
            try {
                List<TradeCalendarRecord> provinceRecords = querySingleProvinceTradeCalendar(date, provinceId);
                if (!provinceRecords.isEmpty()) {
                    // 使用流式操作标记省份信息并收集记录
                    allRecords.addAll(provinceRecords.stream()
                            .peek(record -> {
                                if (record.getProvinceId() == null) {
                                    record.setProvinceId(provinceId);
                                }
                            })
                            .collect(Collectors.toList()));
                }
            } catch (Exception e) {
                log.warn("查询省份{}的交易日历数据失败: {}", provinceId, e.getMessage(), e);  // 添加异常堆栈
                // 继续查询其他省份，不因为一个省份失败而中断
            }
        });

// 使用Comparator进行优雅排序
        allRecords.sort(Comparator.comparing(
                TradeCalendarRecord::getTradeDate,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));

        log.info("全国交易日历汇总查询完成 - 总记录数: {}", allRecords.size());
        return allRecords;
    }

    /**
     * 查询单个省份的交易日历数据
     */
    private List<TradeCalendarRecord> querySingleProvinceTradeCalendar(String date, Integer provinceId) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            throw new RuntimeException("不支持的省份ID: " + provinceId);
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            log.info("查询单省份交易日历 - 省份ID: {}, 数据源: {}, 日期: {}",
                    provinceId, dsKey, date != null ? date : "全部");

            List<TradeCalendarRecord> result;
            if (date != null) {
                // 如果指定了日期，按日期查询
                LambdaQueryWrapper<TradeCalendarRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TradeCalendarRecord::getTradeDate, date);
                result = tradeCalendarRecordService.list(queryWrapper);
            } else {
                // 如果没有指定日期，查询所有记录
                result = tradeCalendarRecordService.list();
            }

            // 为查询结果设置省份ID（因为provinceId不是数据库字段，需要手动设置）
            result.forEach(record -> record.setProvinceId(provinceId));

            return result;
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

}