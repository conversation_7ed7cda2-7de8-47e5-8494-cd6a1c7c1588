package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.MarketNotice;
import org.jeecg.modules.api.power_trade.entity.NoticeType;
import org.jeecg.modules.api.power_trade.vo.MarketNoticeVO;

import java.util.List;

public interface MarketNoticeService extends IService<MarketNotice> {

    /**
     * 获取公告列表
     *
     * @param page 分页参数
     * @param provinceId 省份ID
     * @param typeId 公告类型ID
     * @param keyword 关键词
     * @return 公告列表
     */
    IPage<MarketNoticeVO> getAnnouncementList(Page<MarketNotice> page, Integer typeId, Integer provinceId, String keyword);

    /**
     * 获取公告详情
     *
     * @param id 公告ID
     * @return 公告详情
     */
    MarketNoticeVO getAnnouncementDetail(Long id);

    /**
     * 根据类型获取公告列表
     *
     * @param typeId 公告类型ID
     * @param limit 限制数量
     * @return 公告列表
     */
    List<MarketNoticeVO> getAnnouncementsByType(Integer typeId, Integer limit);

    /**
     * 获取所有公告类型
     *
     * @return 公告类型列表
     */
    List<NoticeType> getAllNoticeTypesList();
}