package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import kotlin.jvm.Transient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostLoad;
import java.util.Date;

@Data
@TableName("notice_file")
public class NoticeFile {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("file_name")
    @ApiModelProperty(value = "文件名称", example = "电力市场交易公告.pdf")
    private String fileName;

    @TableField("file_url")
    @ApiModelProperty(value = "文件URL地址", example = "http://example.com/files/notice/2023/01/01/电力市场交易公告.pdf")
    private String fileUrl;

    @TableField("notice_id")
    @ApiModelProperty(value = "关联公告ID", example = "1001")
    private Long noticeId;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private Date updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

}