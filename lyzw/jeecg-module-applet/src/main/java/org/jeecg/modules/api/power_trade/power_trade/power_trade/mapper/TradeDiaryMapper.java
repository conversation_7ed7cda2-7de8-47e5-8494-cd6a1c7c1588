package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.TradeDiary;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TradeDiaryMapper extends BaseMapper<TradeDiary> {
    
    /**
     * 根据日期范围查询交易日历数据，并关联省份信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param provinceIds 省份ID列表，为空则查询所有
     * @return 交易日历列表
     */
    List<TradeDiary> selectTradeDiaryByDateRange(
            @Param("startDate") Date startDate, 
            @Param("endDate") Date endDate, 
            @Param("provinceIds") List<Integer> provinceIds);
    
    /**
     * 根据日期和省份ID查询交易日历
     * @param targetDate 日期
     * @param provinceId 省份ID，为空则查询所有
     * @return 交易日历列表
     */
    List<TradeDiary> selectTradeDiaryByDateAndProvince(
            @Param("targetDate") Date targetDate, 
            @Param("provinceId") Integer provinceId);
            
    /**
     * 分页查询省份的交易日历数据（返回Map类型）
     * @param provinceId 省份ID
     * @param pageSize 每页记录数
     * @param offset 偏移量
     * @return 交易日历数据列表
     */
    List<Map<String, Object>> getTradeCalendarByProvinceWithPage(
            @Param("provinceId") Integer provinceId,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);
            
    /**
     * 统计符合条件的总记录数
     * @param provinceId 省份ID
     * @return 记录总数
     */
    int countTradeCalendarByProvince(@Param("provinceId") Integer provinceId);
} 