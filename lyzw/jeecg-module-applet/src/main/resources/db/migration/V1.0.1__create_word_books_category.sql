-- 创建词书分类表
CREATE TABLE `inz_word_books_category` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类代码',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父分类ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` int DEFAULT 1 COMMENT '是否启用（0-禁用，1-启用）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='词书分类表';

-- 添加默认分类数据
INSERT INTO `inz_word_books_category` (`id`, `name`, `code`, `description`, `parent_id`, `sort`, `status`) VALUES
('1', '大学英语', 'COLLEGE', '大学英语相关词书', NULL, 1, 1),
('2', '考试词汇', 'EXAM', '各类考试词汇', NULL, 2, 1),
('3', '专业英语', 'PROFESSIONAL', '专业领域英语词汇', NULL, 3, 1),
('4', '基础词汇', 'BASIC', '基础英语词汇', NULL, 4, 1);

-- 添加子分类
INSERT INTO `inz_word_books_category` (`id`, `name`, `code`, `description`, `parent_id`, `sort`, `status`) VALUES
('101', '大学英语四级', 'CET4', '大学英语四级词汇', '1', 1, 1),
('102', '大学英语六级', 'CET6', '大学英语六级词汇', '1', 2, 1),
('201', '考研英语', 'GRADUATE', '考研英语词汇', '2', 1, 1),
('202', '专八词汇', 'TEM8', '英语专业八级词汇', '2', 2, 1),
('203', '雅思词汇', 'IELTS', '雅思考试词汇', '2', 3, 1),
('204', '托福词汇', 'TOEFL', '托福考试词汇', '2', 4, 1);

-- 修改词书表，添加分类相关字段
ALTER TABLE `inz_word_books`
ADD COLUMN `category_id` varchar(36) DEFAULT NULL COMMENT '分类ID' AFTER `education_id`,
ADD COLUMN `level` int DEFAULT 1 COMMENT '难度等级（1-初级，2-中级，3-高级）' AFTER `category_id`,
ADD COLUMN `tags` varchar(200) DEFAULT NULL COMMENT '标签（多个标签用逗号分隔）' AFTER `level`,
ADD CONSTRAINT `fk_word_books_category` FOREIGN KEY (`category_id`) REFERENCES `inz_word_books_category` (`id`); 