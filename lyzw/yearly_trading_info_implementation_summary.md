# 年度交易电量信息接口实现总结

## 实现时间
2025-07-27 23:45

## 需求分析

### 业务需求
根据您的需求，年度交易电量信息接口需要实现以下功能：

1. **筛选条件**：年日期选择器，默认展示当年
2. **展示字段**：
   - 年度交易总电量
   - 年度交易均价
   - 每个月的数据（月度交易电量和月度交易均价）
3. **数据来源**：`power_side_settle`表
4. **计算规则**：
   - 月度交易电量：`settlement_electricity`字段
   - 月度交易均价：`settlement_electric_fee` ÷ `settlement_electricity`
   - 年度交易电量：月度交易电量求和
   - 年度交易均价：`settlement_electric_fee`求和 ÷ `settlement_electricity`求和
5. **关联逻辑**：先通过电站ID和settle_date去`file_station_relation`表获取file_id，然后使用file_id获取相关信息

### 技术实现

#### 1. 数据库表关联结构
```sql
file_station_relation (文件关联电站表)
├── id (主键，作为file_id)
├── station_id (电站ID)
├── settle_date (结算日期)
└── type (文件类型：3-月统推发电侧结算单, 4-月统推独立储能结算单)

power_side_settle (统推发电侧结算单)
├── id (主键)
├── file_id (关联file_station_relation.id)
├── settlement_electricity (结算电量 - 用于计算)
└── settlement_electric_fee (结算电费 - 用于计算)
```

#### 2. SQL查询实现
**文件位置**：`PowerSideSettleMapper.xml`

```sql
<!-- 获取电站年度交易电量信息（用于电站详情） -->
<select id="getStationYearlyTradingInfo" resultType="java.util.Map">
    SELECT
        DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
        -- 月度交易电量：settlement_electricity字段求和
        COALESCE(SUM(pss.settlement_electricity), 0) as monthlyElectricity,
        -- 月度交易均价：settlement_electric_fee求和 / settlement_electricity求和
        CASE
            WHEN SUM(pss.settlement_electricity) > 0
            THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
            ELSE 0
        END as monthlyAveragePrice,
        -- 月度交易总电费：settlement_electric_fee字段求和
        COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlyTotalFee,
        -- 其他辅助字段
        COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
        COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
        COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
        COUNT(*) as recordCount
    FROM power_side_settle pss
    INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
    WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND fsr.type IN (3, 4)  <!-- 月统推结算单类型 -->
        AND pss.settlement_electricity IS NOT NULL
        AND pss.settlement_electricity > 0
    GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
    ORDER BY month ASC
</select>
```

#### 3. 业务逻辑实现
**文件位置**：`PowerSideSettleServiceImpl.java`

```java
@Override
public Map<String, Object> getStationYearlyTradingInfo(Long stationId, String year) {
    // 验证年份格式
    if (!year.matches("\\d{4}")) {
        throw new IllegalArgumentException("年份格式不正确，请使用yyyy格式");
    }

    // 获取按月分解的数据
    log.info("查询年度交易信息 - 电站ID: {}, 年份: {}", stationId, year);
    List<Map<String, Object>> monthlyDataList = baseMapper.getStationYearlyTradingInfo(stationId, year);

    // 计算年度汇总数据
    BigDecimal yearlyTotalElectricity = BigDecimal.ZERO;  // 年度交易总电量 = 月度交易电量求和
    BigDecimal yearlyTotalFee = BigDecimal.ZERO;          // 年度交易总电费 = settlement_electric_fee求和

    List<String> months = new ArrayList<>();
    List<BigDecimal> electricityList = new ArrayList<>();
    List<BigDecimal> averagePriceList = new ArrayList<>();

    if (monthlyDataList != null) {
        for (Map<String, Object> monthData : monthlyDataList) {
            // 从数据库查询结果中获取字段值
            BigDecimal monthlyElectricity = (BigDecimal) monthData.getOrDefault("monthlyElectricity", BigDecimal.ZERO);
            BigDecimal monthlyFee = (BigDecimal) monthData.getOrDefault("monthlyTotalFee", BigDecimal.ZERO);
            BigDecimal monthlyPrice = (BigDecimal) monthData.getOrDefault("monthlyAveragePrice", BigDecimal.ZERO);
            String month = (String) monthData.get("month");

            // 累加年度数据
            yearlyTotalElectricity = yearlyTotalElectricity.add(monthlyElectricity != null ? monthlyElectricity : BigDecimal.ZERO);
            yearlyTotalFee = yearlyTotalFee.add(monthlyFee != null ? monthlyFee : BigDecimal.ZERO);

            // 收集月度数据用于图表展示
            months.add(month != null ? month : "");
            electricityList.add(monthlyElectricity != null ? monthlyElectricity : BigDecimal.ZERO);
            averagePriceList.add(monthlyPrice != null ? monthlyPrice : BigDecimal.ZERO);
        }
    }

    // 计算年度交易均价：settlement_electric_fee求和 / settlement_electricity求和
    BigDecimal yearlyAveragePrice = BigDecimal.ZERO;
    if (yearlyTotalElectricity.compareTo(BigDecimal.ZERO) > 0) {
        yearlyAveragePrice = yearlyTotalFee.divide(yearlyTotalElectricity, 6, BigDecimal.ROUND_HALF_UP);
    }

    // 构建返回结果
    Map<String, Object> result = new HashMap<>();
    result.put("year", year);
    result.put("yearlyTotalElectricity", yearlyTotalElectricity);
    result.put("yearlyAveragePrice", yearlyAveragePrice);
    result.put("yearlyTotalFee", yearlyTotalFee);
    result.put("monthlyData", monthlyDataList);

    // 构建图表数据
    Map<String, Object> chartData = new HashMap<>();
    chartData.put("months", months);
    chartData.put("electricity", electricityList);
    chartData.put("averagePrice", averagePriceList);
    result.put("chartData", chartData);

    return result;
}
```

#### 4. 控制器接口
**文件位置**：`StationController.java`

```java
/**
 * 获取电站年度交易电量信息
 */
@GetMapping("/{id}/yearly-trading-info")
@ApiOperation(value = "获取电站年度交易电量信息", notes = "获取电站的年度交易信息，包含月度分解数据")
public Result<Map<String, Object>> getStationYearlyTradingInfo(
        @ApiParam(value = "电站ID", required = true) @PathVariable Long id,
        @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId,
        @ApiParam(value = "年份", required = true) @RequestParam String year) {
    try {
        // 参数验证
        if (id == null || id <= 0) {
            return Result.error("电站ID不能为空且必须大于0");
        }
        if (provinceId == null) {
            return Result.error("省份ID不能为空");
        }
        if (year == null || !year.matches("\\d{4}")) {
            return Result.error("年份格式不正确，请使用yyyy格式");
        }

        // 切换到对应省份的数据源
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);

        // 验证电站是否属于该省份
        Station station = stationService.getById(id);
        if (station == null) {
            return Result.error("电站不存在");
        }
        if (!station.getProvinceId().equals(provinceId)) {
            return Result.error("电站不属于指定省份");
        }

        // 查询年度交易电量信息
        Map<String, Object> tradingInfo = powerSideSettleService.getStationYearlyTradingInfo(id, year);

        // 添加额外信息
        tradingInfo.put("stationInfo", station);
        tradingInfo.put("hasData", tradingInfo.get("monthlyData") != null &&
                       !((List<?>) tradingInfo.get("monthlyData")).isEmpty());

        return Result.OK(tradingInfo);

    } catch (Exception e) {
        log.error("获取电站年度交易电量信息失败 - 电站ID: {}, 年份: {}", id, year, e);
        return Result.error("获取年度交易电量信息失败：" + e.getMessage());
    } finally {
        DynamicDataSourceContextHolder.clear();
    }
}
```

## 接口使用说明

### 请求示例
```bash
GET /electricity/api/power_trade/stations/{id}/yearly-trading-info?provinceId=1&year=2024
```

### 请求参数
- `id`: 电站ID (路径参数)
- `provinceId`: 省份ID (查询参数)
- `year`: 年份，格式为yyyy (查询参数)

### 响应格式
```json
{
  "success": true,
  "message": "",
  "code": 200,
  "result": {
    "year": "2024",
    "yearlyTotalElectricity": 54403.54,
    "yearlyAveragePrice": 474.74,
    "yearlyTotalFee": 25834567.89,
    "hasData": true,
    "stationInfo": {
      "id": 5,
      "name": "测试电站",
      "type": 1,
      "provinceId": 1
    },
    "monthlyData": [
      {
        "month": "2024-01",
        "monthlyElectricity": 4500.12,
        "monthlyAveragePrice": 450.25,
        "monthlyTotalFee": 2025054.00,
        "actualInternetElectricity": 4600.00,
        "contractElectricity": 4400.00,
        "deviationElectricity": 100.12,
        "recordCount": 1
      },
      {
        "month": "2024-02",
        "monthlyElectricity": 4200.34,
        "monthlyAveragePrice": 465.80,
        "monthlyTotalFee": 1957038.52,
        "actualInternetElectricity": 4300.00,
        "contractElectricity": 4100.00,
        "deviationElectricity": 100.34,
        "recordCount": 1
      }
      // ... 其他月份数据
    ],
    "chartData": {
      "months": ["2024-01", "2024-02", "2024-03", "2024-04"],
      "electricity": [4500.12, 4200.34, 5100.45, 4800.67],
      "averagePrice": [450.25, 465.80, 480.15, 470.30]
    }
  },
  "timestamp": 1753666789123
}
```

### 响应字段说明
- `yearlyTotalElectricity`: 年度交易总电量 (MWh)
- `yearlyAveragePrice`: 年度交易均价 (元/MWh)
- `yearlyTotalFee`: 年度交易总电费 (元)
- `monthlyData`: 月度数据列表
  - `month`: 月份 (yyyy-MM格式)
  - `monthlyElectricity`: 月度交易电量 (MWh)
  - `monthlyAveragePrice`: 月度交易均价 (元/MWh)
  - `monthlyTotalFee`: 月度交易总电费 (元)
- `chartData`: 图表数据，用于前端绘制图表
  - `months`: 月份数组
  - `electricity`: 月度交易电量数组
  - `averagePrice`: 月度交易均价数组

## 技术特点

### 1. 数据准确性
- ✅ **字段映射正确**：使用`settlement_electricity`作为交易电量
- ✅ **计算逻辑准确**：月度均价和年度均价计算符合业务需求
- ✅ **关联查询正确**：通过`file_station_relation`表正确关联数据

### 2. 性能优化
- ✅ **SQL优化**：使用GROUP BY按月聚合，减少数据传输
- ✅ **索引利用**：利用station_id和settle_date的索引
- ✅ **数据源切换**：支持多省份数据源动态切换

### 3. 异常处理
- ✅ **参数验证**：严格的参数格式验证
- ✅ **数据校验**：电站归属省份验证
- ✅ **异常捕获**：完善的异常处理机制

### 4. 调试支持
- ✅ **调试查询**：提供debugStationDataRelation方法用于问题排查
- ✅ **日志记录**：详细的日志记录便于问题定位
- ✅ **数据验证**：返回记录数等统计信息

## 部署验证

### 1. 编译验证
```bash
cd lyzw
mvn clean compile
```

### 2. 功能测试
```bash
# 测试年度交易电量信息接口
curl "http://localhost:10015/electricity/api/power_trade/stations/5/yearly-trading-info?provinceId=1&year=2024"
```

### 3. 数据验证
```sql
-- 验证数据关联
SELECT 
    fsr.station_id,
    fsr.settle_date,
    COUNT(pss.id) as record_count,
    SUM(pss.settlement_electricity) as total_electricity,
    SUM(pss.settlement_electric_fee) as total_fee
FROM file_station_relation fsr
LEFT JOIN power_side_settle pss ON fsr.id = pss.file_id
WHERE fsr.station_id = 5 
AND YEAR(fsr.settle_date) = 2024
AND fsr.type IN (3, 4)
GROUP BY fsr.station_id, fsr.settle_date
ORDER BY fsr.settle_date;
```

## 后续优化建议

### 1. 缓存优化
```java
@Cacheable(value = "yearlyTradingInfo", key = "#stationId + '_' + #year")
public Map<String, Object> getStationYearlyTradingInfo(Long stationId, String year) {
    // 实现逻辑...
}
```

### 2. 分页支持
```java
// 对于数据量大的情况，可以考虑分页
public PageResult<Map<String, Object>> getStationYearlyTradingInfoWithPaging(
        Long stationId, String year, Integer pageNo, Integer pageSize) {
    // 实现逻辑...
}
```

### 3. 数据导出
```java
// 支持Excel导出功能
public void exportYearlyTradingInfo(Long stationId, String year, HttpServletResponse response) {
    // 实现逻辑...
}
```

## 总结

通过这次实现，成功构建了完整的年度交易电量信息接口：

- 🎯 **需求实现**：完全符合业务需求的数据计算和展示
- 🚀 **性能优化**：高效的SQL查询和数据聚合
- 💎 **代码质量**：清晰的代码结构和完善的异常处理
- 🛡️ **数据准确性**：严格按照业务规则进行数据计算
- 📈 **可扩展性**：支持多省份、多电站的数据查询

现在接口已经完全实现，可以为前端提供准确的年度交易电量数据，支持图表展示和数据分析。
